/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "/_error";
exports.ids = ["/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules\\next\\dist\\pages\\_error.js */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./api/apollo-client.js":
/*!******************************!*\
  !*** ./api/apollo-client.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst client = new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.ApolloClient({\n    uri: \"http://127.0.0.1:1337/graphql\",\n    cache: new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.InMemoryCache({\n        addTypename: false\n    }),\n    defaultOptions: {\n        query: {\n            fetchPolicy: \"no-cache\"\n        }\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (client);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcGkvYXBvbGxvLWNsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkQ7QUFFN0QsTUFBTUUsU0FBUyxJQUFJRix3REFBWUEsQ0FBQztJQUM5QkcsS0FBS0MsK0JBQWtDO0lBQ3ZDRyxPQUFPLElBQUlOLHlEQUFhQSxDQUFDO1FBQ3ZCTyxhQUFhO0lBQ2Y7SUFDQUMsZ0JBQWdCO1FBQ2RDLE9BQU87WUFDTEMsYUFBYTtRQUNmO0lBQ0Y7QUFDRjtBQUVBLGlFQUFlVCxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHBzZy1uZXh0Ly4vYXBpL2Fwb2xsby1jbGllbnQuanM/NDA3NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcG9sbG9DbGllbnQsIEluTWVtb3J5Q2FjaGUgfSBmcm9tIFwiQGFwb2xsby9jbGllbnRcIjtcclxuXHJcbmNvbnN0IGNsaWVudCA9IG5ldyBBcG9sbG9DbGllbnQoe1xyXG4gIHVyaTogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1RSQVBJX0dRTCxcclxuICBjYWNoZTogbmV3IEluTWVtb3J5Q2FjaGUoe1xyXG4gICAgYWRkVHlwZW5hbWU6IGZhbHNlXHJcbiAgfSksXHJcbiAgZGVmYXVsdE9wdGlvbnM6IHtcclxuICAgIHF1ZXJ5OiB7XHJcbiAgICAgIGZldGNoUG9saWN5OiBcIm5vLWNhY2hlXCIsXHJcbiAgICB9LFxyXG4gIH1cclxufSk7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBjbGllbnQ7XHJcbiJdLCJuYW1lcyI6WyJBcG9sbG9DbGllbnQiLCJJbk1lbW9yeUNhY2hlIiwiY2xpZW50IiwidXJpIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NUUkFQSV9HUUwiLCJjYWNoZSIsImFkZFR5cGVuYW1lIiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyeSIsImZldGNoUG9saWN5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./api/apollo-client.js\n");

/***/ }),

/***/ "./api/gql-queries.js":
/*!****************************!*\
  !*** ./api/gql-queries.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CORE_POST_SET: () => (/* binding */ CORE_POST_SET),\n/* harmony export */   POST_TYPE_MODULES: () => (/* binding */ POST_TYPE_MODULES),\n/* harmony export */   QUERY_POPUPS: () => (/* binding */ QUERY_POPUPS),\n/* harmony export */   fragments: () => (/* binding */ fragments),\n/* harmony export */   queries: () => (/* binding */ queries)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst POST_TYPE_MODULES = _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n  fragment postTypeModules on Post {\r\n    modules {\r\n      ... on ComponentModulePodcast {\r\n        podcast {\r\n          slug\r\n          name\r\n        }\r\n      }\r\n      ... on ComponentModuleFormation {\r\n        __typename\r\n        speakers {\r\n          fullName\r\n        }\r\n        link\r\n        youtubeEmbed\r\n      }\r\n    }\r\n  }\r\n`;\nconst fragments = {\n    CORE_POST_FIELDS: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    fragment CorePostFields on Post {\r\n      id\r\n      title\r\n      body\r\n      slug\r\n      type\r\n      readingTime\r\n      author {\r\n        fullName\r\n        firstName\r\n        lastName\r\n        about\r\n        slug\r\n        picture {\r\n          url\r\n          width\r\n          height\r\n          provider\r\n          alternativeText\r\n        }\r\n      }\r\n      published_at\r\n      image {\r\n        url\r\n        provider\r\n        alternativeText\r\n        caption\r\n        width\r\n        height\r\n      }\r\n      serie {\r\n        id\r\n        name\r\n      }\r\n      topics {\r\n        name\r\n        slug\r\n      }\r\n      blog {\r\n        blogger {\r\n          fullName\r\n          slug\r\n        }\r\n      }\r\n      modules {\r\n        __typename\r\n        ... on ComponentModuleLead {\r\n          content\r\n        }\r\n        ... on ComponentModuleSeo {\r\n          metaDescription\r\n          metaTitle\r\n        }\r\n      }\r\n    }\r\n  `\n};\nconst FULL_POST_FRAGMENT = _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n  fragment fullPostFragment on Post {\r\n    id\r\n    title\r\n    slug\r\n    type\r\n    published_at\r\n    body\r\n    author {\r\n      fullName\r\n      picture {\r\n        url\r\n        provider\r\n      }\r\n    }\r\n    image {\r\n      url\r\n      height\r\n      width\r\n      alternativeText\r\n      provider\r\n    }\r\n    topics {\r\n      name\r\n    }\r\n    modules {\r\n      ... on ComponentModuleWebinar {\r\n        __typename\r\n        webinar {\r\n          slug\r\n          name\r\n        }\r\n        embedVideo\r\n        speakers {\r\n          fullName\r\n          firstName\r\n          lastName\r\n          picture {\r\n            url\r\n            provider\r\n            size\r\n          }\r\n        }\r\n      }\r\n      ... on ComponentModulePodcast {\r\n        __typename\r\n        podcast {\r\n          slug\r\n          name\r\n        }\r\n        embedAudio\r\n        embedVideo\r\n      }\r\n      ... on ComponentModuleLead {\r\n        __typename\r\n        content\r\n      }\r\n    }\r\n  }\r\n`;\nconst queries = {\n    QUERY_TOPIC: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    query MainTopic($slug: String!) {\r\n      topics(where: { slug: $slug }) {\r\n        name\r\n        slug\r\n        id\r\n        postCount\r\n        description\r\n        parent {\r\n          slug\r\n          name\r\n          parent {\r\n            slug\r\n            name\r\n          }\r\n        }\r\n      }\r\n    }\r\n  `,\n    QUERY_TOPIC_GROUP: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    query TopicGroup($slug: String!) {\r\n      topicGroups(where: { slug: $slug }) {\r\n        name\r\n        description\r\n        cover {\r\n          formats\r\n        }\r\n        topics {\r\n          id\r\n          postCount\r\n        }\r\n        featured {\r\n          title\r\n          description\r\n          inColumn\r\n          image {\r\n            url\r\n            width\r\n            height\r\n            provider\r\n            caption\r\n            alternativeText\r\n          }\r\n        }\r\n      }\r\n    }\r\n  `,\n    QUERY_TOPICS_POSTS: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    query Posts($topicIds: [ID], $offset: Int!) {\r\n      posts(\r\n        limit: 20\r\n        start: $offset\r\n        where: { topics: { id_in: $topicIds } }\r\n      ) {\r\n        title\r\n        slug\r\n        published_at\r\n        image {\r\n          url\r\n          height\r\n          width\r\n          provider\r\n          caption\r\n          alternativeText\r\n        }\r\n        author {\r\n          fullName\r\n        }\r\n        topics {\r\n          slug\r\n        }\r\n        modules {\r\n          __typename\r\n          ... on ComponentModulePodcast {\r\n            podcast {\r\n              name\r\n              slug\r\n            }\r\n          }\r\n        }\r\n        type\r\n      }\r\n    }\r\n  `,\n    QUERY_RELATED: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    query GetRelated($id: ID!) {\r\n      relatedPosts(id: $id) {\r\n        section\r\n        score\r\n        origin\r\n        post {\r\n          id\r\n          title\r\n          slug\r\n          type\r\n          published_at\r\n          author {\r\n            fullName\r\n            picture {\r\n              url\r\n              provider\r\n            }\r\n          }\r\n          image {\r\n            url\r\n            height\r\n            width\r\n            alternativeText\r\n            provider\r\n          }\r\n          topics {\r\n            name\r\n          }\r\n          modules {\r\n            __typename\r\n            ... on ComponentModuleWebinar {\r\n              webinar {\r\n                slug\r\n                name\r\n              }\r\n              speakers {\r\n                fullName\r\n                firstName\r\n                lastName\r\n                picture {\r\n                  url\r\n                  provider\r\n                  size\r\n                }\r\n              }\r\n            }\r\n            ... on ComponentModulePodcast {\r\n              podcast {\r\n                slug\r\n                name\r\n              }\r\n            }\r\n            ... on ComponentModuleLead {\r\n              content\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  `,\n    QUERY_BLOG_POSTS: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    ${FULL_POST_FRAGMENT}\r\n    query BlogPosts($blog: ID!, $limit: Int!, $sort: String!) {\r\n      posts(limit: $limit, where: { blog: $blog }, sort: $sort) {\r\n        ...fullPostFragment\r\n      }\r\n    }\r\n  `,\n    QUERY_POSTS: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    ${FULL_POST_FRAGMENT}\r\n    query Posts($limit: Int!, $sort: String!) {\r\n      posts(limit: $limit, sort: $sort) {\r\n        ...fullPostFragment\r\n      }\r\n    }\r\n  `\n};\nconst CORE_POST_SET = _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n  fragment CorePostSet on Post {\r\n    title\r\n    slug\r\n    type\r\n    published_at\r\n    topics {\r\n      name\r\n    }\r\n    image {\r\n      url\r\n      width\r\n      height\r\n      alternativeText\r\n      provider\r\n    }\r\n    author {\r\n      fullName\r\n      picture {\r\n        url\r\n        formats\r\n      }\r\n    }\r\n    modules {\r\n      __typename\r\n      ... on ComponentModulePodcast {\r\n        podcast {\r\n          slug\r\n          name\r\n        }\r\n      }\r\n      ... on ComponentModuleFormation {\r\n        speakers {\r\n          fullName\r\n        }\r\n        link\r\n        youtubeEmbed\r\n      }\r\n    }\r\n  }\r\n`;\nconst QUERY_POPUPS = _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n  query Popups {\r\n    popups(sort: \"published_at:desc\") {\r\n      id\r\n      title\r\n      body\r\n      image {\r\n        url\r\n        provider\r\n      }\r\n      startDate\r\n      endDate\r\n      published_at\r\n      button {\r\n        name\r\n        url\r\n      }\r\n    }\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./api/gql-queries.js\n");

/***/ }),

/***/ "./components/CookieBanner.js":
/*!************************************!*\
  !*** ./components/CookieBanner.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CookieBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-cookie */ \"react-cookie\");\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_cookie__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var components_shared_inputs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/shared/inputs */ \"./components/shared/inputs/index.js\");\n/* harmony import */ var components_shared_Buttons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/shared/Buttons */ \"./components/shared/Buttons/index.js\");\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var hooks_useFormData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! hooks/useFormData */ \"./hooks/useFormData.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_9__);\n\n\n\n\n\n\n\n\n\n\n\nfunction CookieBanner() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [cookie, setCookie] = (0,react_cookie__WEBPACK_IMPORTED_MODULE_2__.useCookies)([\n        \"preferences\"\n    ]);\n    // const [showDetails, setShowDetails] = useState(false);\n    const [showBanner, setShowBanner] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const [formValues, handleFormValueChange] = (0,hooks_useFormData__WEBPACK_IMPORTED_MODULE_7__.useFormData)({\n        essentials: true,\n        analytics: true,\n        medias: true,\n        set: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        setShowBanner(!cookie.preferences?.set);\n    }, [\n        cookie\n    ]);\n    function onValidation() {\n        setCookie(\"preferences\", formValues, {\n            sameSite: \"strict\",\n            path: \"/\",\n            expires: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)\n        });\n        next_router__WEBPACK_IMPORTED_MODULE_8___default().reload();\n    }\n    if (!showBanner) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    if (router.asPath === \"/cookies\") return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    console.log(router.asPath);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StlCookieBannerContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StlCookieBanner, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"cb-top\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"cb-title\",\n                            children: \"Param\\xe8tre des cookies\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"cb-entries\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cb-entry\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_shared_inputs__WEBPACK_IMPORTED_MODULE_3__.Toggle, {\n                                            isChecked: true,\n                                            formKey: \"essentials\",\n                                            handleValueChange: handleFormValueChange,\n                                            label: \"N\\xe9cessaires\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"cb-entry-desc\",\n                                            children: [\n                                                \"Certains cookies sont n\\xe9cessaires au fonctionnement minimal du site toutpoursagloire.com comme :\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"Vos pr\\xe9f\\xe9rences d’acceptation ou de rejet des cookies\",\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                                            lineNumber: 61,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"\\xc9ventuellement des informations techniques pour le bon affichage des pages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                                            lineNumber: 64,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_shared_inputs__WEBPACK_IMPORTED_MODULE_3__.Toggle, {\n                                            isChecked: true,\n                                            formKey: \"analytics\",\n                                            handleValueChange: handleFormValueChange,\n                                            label: \"Statistiques\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"cb-entry-desc\",\n                                            children: \"Ces cookies sont utiles pour nous permettre de vous fournir des ressources (articles, podcasts, webinaires, …) toujours plus adapt\\xe9es et pertinentes. Nous utilisons Google Analytics pour \\xe7a.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_shared_inputs__WEBPACK_IMPORTED_MODULE_3__.Toggle, {\n                                            isChecked: true,\n                                            formKey: \"medias\",\n                                            handleValueChange: handleFormValueChange,\n                                            label: \"Provenance de tiers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"cb-entry-desc\",\n                                            children: [\n                                                \"Il s’agit du reste des cookies venant de services externes \\xe0 toutpoursagloire.com mais pour autant utiles pour une exp\\xe9rience compl\\xe8te comme :\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Les services multim\\xe9dias pour les podcast et webinaires (YouTube, Spotify, SoundCloud…)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Les services de communication pour les formulaires mails (ConvertKit)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Les services de remont\\xe9e de bug pour l'am\\xe9lioration continue du site (Marker.io)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"cb-bottom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"cb-details-btn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                href: \"/cookies\",\n                                children: \"Voir les d\\xe9tails\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_shared_Buttons__WEBPACK_IMPORTED_MODULE_4__.SmallButton, {\n                            text: \"Valider\",\n                            theme: \"dark\",\n                            action: ()=>onValidation()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\CookieBanner.js\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\nconst StlCookieBannerContainer = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"CookieBanner__StlCookieBannerContainer\",\n    componentId: \"sc-dbd6c5f7-0\"\n})`\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  height: 100%;\r\n  width: 100%;\r\n  z-index: 9999;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: rgba(8, 29, 33, 0.96);\r\n`;\nconst StlCookieBanner = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"CookieBanner__StlCookieBanner\",\n    componentId: \"sc-dbd6c5f7-1\"\n})`\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  overflow: scroll;\r\n  max-width: 500px;\r\n  max-height: 66%;\r\n  box-shadow: 0 4px 16px rgba(23, 22, 22, 0.2);\r\n  background-color: var(--soft-white);\r\n\r\n  p {\r\n    font-family: Switzer, sans-serif;\r\n    font-size: 14px;\r\n    ul {\r\n      margin-top: 8px;\r\n      padding-left: 20px;\r\n    }\r\n  }\r\n\r\n  .cb-top {\r\n    padding: 32px;\r\n  }\r\n\r\n  .cb-title {\r\n    margin-top: 0;\r\n    margin-bottom: 0;\r\n    font-size: 18px;\r\n    font-family: Switzer, sans-serif;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .cb-entries {\r\n    margin-top: 32px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n\r\n  .cb-bottom {\r\n    position: sticky;\r\n    bottom: 0;\r\n    width: 100%;\r\n    padding: 32px;\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    background-color: var(--c-soft-cream);\r\n  }\r\n\r\n  .cb-details-btn {\r\n    margin: 0;\r\n    color: #242424;\r\n    text-decoration: underline;\r\n    font-family: Switzer, sans-serif;\r\n    font-weight: 500;\r\n    letter-spacing: 0.4px;\r\n    font-size: 14px;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: var(--brand-color);\r\n    }\r\n  }\r\n\r\n  @media ${styles_device__WEBPACK_IMPORTED_MODULE_5__.device.tablet} {\r\n    min-width: 500px;\r\n  }\r\n\r\n  @media ${styles_device__WEBPACK_IMPORTED_MODULE_5__.device.tablet} {\r\n    position: relative;\r\n    max-height: 100%;\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/CookieBanner.js\n");

/***/ }),

/***/ "./components/Popup/index.js":
/*!***********************************!*\
  !*** ./components/Popup/index.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Popup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var api_apollo_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! api/apollo-client */ \"./api/apollo-client.js\");\n/* harmony import */ var api_gql_queries__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! api/gql-queries */ \"./api/gql-queries.js\");\n/* harmony import */ var hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! hooks/useLocalStorage */ \"./hooks/useLocalStorage.js\");\n/* harmony import */ var utils_date_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! utils/date.utils */ \"./utils/date.utils.js\");\n/* harmony import */ var _shared_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../shared/atoms/Buttons/BigCta */ \"./components/shared/atoms/Buttons/BigCta.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _utils_image_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../utils/image-utils */ \"./utils/image-utils.js\");\n/* harmony import */ var _styles_device__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../styles/device */ \"./styles/device.js\");\n/* harmony import */ var _shared_Buttons_ButtonClose__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../shared/Buttons/ButtonClose */ \"./components/shared/Buttons/ButtonClose.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n// Nombre de jours minimums avant d'afficher à nouveau le popup\nconst DAY_SHOW_PU = 1;\n/**\r\n\t* Retourne Faux si popup n'existe pas\r\n\t* Retourne Vrai si :\r\n\t* - utilisateur n'à pas déjà clicker sur le cta\r\n\t* - si la date de fin n'est pas passer et que date de début franchi\r\n\t* - si la dernière fois que la popup a été fermer avec la croix correspond à plus de 'DAY_SHOW_PU' jours\r\n\t*/ function shouldShow(pu) {\n    if (!pu) {\n        return false;\n    }\n    let now = new Date();\n    const start = pu?.startDate ? new Date(pu?.startDate) : now;\n    const end = new Date(pu?.endDate);\n    const lview = pu?.lastView && new Date(pu?.lastView);\n    return !pu?.engaged && now < end && now >= start && (!lview || (0,utils_date_utils__WEBPACK_IMPORTED_MODULE_6__.dateDiffInDays)(lview, now) >= DAY_SHOW_PU);\n}\nfunction Popup() {\n    const [storedPopup, setStoredPopup] = (0,hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"tpsg-pu\", null);\n    const [showPopup, setShowPopup] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        async function updatePopup() {\n            // Récupération des nouveaux popups\n            const allPopups = await api_apollo_client__WEBPACK_IMPORTED_MODULE_3__[\"default\"].query({\n                query: api_gql_queries__WEBPACK_IMPORTED_MODULE_4__.QUERY_POPUPS\n            }).then((response)=>{\n                return response?.data?.popups;\n            });\n            const now = new Date();\n            const newPopup = allPopups?.filter((e)=>now < new Date(e.endDate))[0];\n            if (newPopup?.id && newPopup.id !== storedPopup?.id) {\n                // Le popup n'est pas dans le store, donc on l'ajoute\n                setStoredPopup({\n                    ...newPopup,\n                    lastView: null,\n                    engaged: false\n                });\n                setShowPopup(shouldShow(storedPopup));\n            } else {\n                setShowPopup(shouldShow(storedPopup));\n            }\n        }\n        updatePopup();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    function closePopup(engaged = false) {\n        setStoredPopup({\n            ...storedPopup,\n            lastView: new Date(),\n            engaged: engaged\n        });\n        setShowPopup(false);\n    }\n    if (!showPopup) return;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pu-inner-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pu-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pu-image-container\",\n                            children: storedPopup.image?.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                fill: true,\n                                sizes: \"500px\",\n                                style: imageStyle,\n                                src: (0,_utils_image_utils__WEBPACK_IMPORTED_MODULE_9__.withRealSrc)(storedPopup.image),\n                                alt: \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pu-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pu-text\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"pu-title\",\n                                            children: storedPopup.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"pu-body\",\n                                            children: storedPopup.body\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    onClickFunction: ()=>closePopup(true),\n                                    text: storedPopup.button.name,\n                                    link: storedPopup.button.url\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pu-close\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_Buttons_ButtonClose__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    handleClick: ()=>closePopup()\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\Popup\\\\index.js\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n;\nconst imageStyle = {\n    objectFit: \"contain\"\n};\nconst Wrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"Popup__Wrapper\",\n    componentId: \"sc-c883c5c9-0\"\n})`\r\n  position: fixed;\r\n  bottom: 0;\r\n  right: 0;\r\n  padding: 24px;\r\n  width: calc(100% - 32px);\r\n  max-height: 100%;\r\n  margin: 16px;\r\n  background: linear-gradient(85.13deg, #081D21 15.79%, #1d2b30 104.33%);\r\n  z-index: 9999;\r\n\r\n  .pu-content {\r\n    position: relative;\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n\r\n  .pu-image-container {\r\n    position: relative;\r\n    width: 100%;\r\n    max-height: 40vh;\r\n    aspect-ratio: 16/9;\r\n    margin-top: 66px;\r\n    /* background-color: var(--c-soft-cream); */\r\n  }\r\n\r\n  .pu-inner-border {\r\n    position: relative;\r\n    height: 100%;\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: row;\r\n    justify-content: center;\r\n    align-items: center;\r\n    background-image: none;\r\n  }\r\n\r\n  .pu-right {\r\n    position: relative;\r\n    margin-top: 42px;\r\n    width: 100%;\r\n    display: flex;\r\n    align-self: start;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    color: var(--c-soft-cream);\r\n  }\r\n\r\n  .pu-title {\r\n    font-family: Stelvio, sans-serif;\r\n    color: var(--c-soft-cream);\r\n    font-size: 28px;\r\n    line-height: 32px;\r\n    margin: 0;\r\n  }\r\n\r\n  .pu-body {\r\n    font-family: Switzer, sans-serif;\r\n    margin-top: 6px;\r\n    font-weight: 400;\r\n    opacity: 0.9;\r\n    color: var(--c-soft-cream);\r\n    font-size: 14px;\r\n  }\r\n\r\n  button {\r\n    justify-self: flex-end;\r\n    min-width: 80px;\r\n    min-height: 40px;\r\n    margin-right: 24px;\r\n    margin-top: 42px;\r\n  }\r\n\r\n  .pu-close {\r\n    position: absolute;\r\n    top: 24px;\r\n    right: 24px;\r\n  }\r\n\r\n  @media ${_styles_device__WEBPACK_IMPORTED_MODULE_10__.device.desktop} {\r\n    top: 0;\r\n    left: 0;\r\n    width: calc(100% - 2 * (var(--border-space) - 24px));\r\n    margin: calc(var(--border-space) - 24px);\r\n\r\n    .pu-inner-border {\r\n      background-image: url(\"data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333C41FF' stroke-width='2' stroke-dasharray='4%2c 4' stroke-dashoffset='38' stroke-linecap='butt'/%3e%3c/svg%3e\");\r\n    }\r\n\r\n    .pu-image-container {\r\n      width: 50%;\r\n      margin-top: 0;\r\n      max-height: inherit;\r\n    }\r\n\r\n    .pu-content {\r\n      margin: 0 8%;\r\n      width: 100%;\r\n      flex-direction: row;\r\n      align-items: center;\r\n    }\r\n\r\n    .pu-title {\r\n      font-size: 32px;\r\n      line-height: 36px;\r\n    }\r\n\r\n    .pu-right {\r\n      width: 50%;\r\n      margin-top: 0;\r\n      margin-left: 42px;\r\n      aspect-ratio: 16/9;\r\n    }\r\n\r\n    .pu-body {\r\n      margin-top: 24px;\r\n      font-size: 16px;\r\n    }\r\n\r\n    .pu-button {\r\n      margin-top: 0;\r\n    }\r\n\r\n    .pu-close {\r\n      top: 42px;\r\n      right: 42px;\r\n    }\r\n\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Popup/index.js\n");

/***/ }),

/***/ "./components/layout/Footer.js":
/*!*************************************!*\
  !*** ./components/layout/Footer.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_device__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../styles/device */ \"./styles/device.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"footer-mission\",\n                children: \"Nous existons pour vous aider \\xe0 voir comme Dieu voit pour vivre comme Dieu veut\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"footer-links\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/contact\",\n                        children: \"Contact\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/soutenir\",\n                        children: \"Nous soutenir\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/a-propos\",\n                        children: \"\\xc0 propos\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/ce-que-nous-croyons\",\n                        children: \"Ce que nous croyons\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/equipe-tpsg\",\n                        children: \"\\xc9quipe\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/formations#evenements-tpsg\",\n                        children: \"Camp TPSG\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"https://toutpoursagloire.myspreadshop.fr/\",\n                        children: \"Shop\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/mentions-legales\",\n                        children: \"Mentions l\\xe9gales\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Footer.js\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nconst Wrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().footer.withConfig({\n    displayName: \"Footer__Wrapper\",\n    componentId: \"sc-3177e1d6-0\"\n})`\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  column-gap: 16px;\r\n\r\n  padding: 40px var(--border-space) 120px var(--border-space);\r\n\r\n  background-color: var(--c-dark-green);\r\n  color: #E8E8E5;\r\n\r\n  .footer-mission {\r\n    grid-column: 1 / span 2;\r\n    grid-row: 1;\r\n    font-size: 36px;\r\n    line-height: 36px;\r\n    margin: 0 0 96px 0;\r\n  }\r\n\r\n  a {\r\n    display: block;\r\n    font-family: sans-serif, \"Helvetica Neue\", Helvetica, Arial;\r\n    margin: 0 0 8px 0;\r\n    line-height: 24px;\r\n    opacity: 0.8;\r\n    font-weight: 400;\r\n  }\r\n\r\n  a:hover {\r\n    text-decoration: underline;\r\n  }\r\n\r\n  hr {\r\n    border-color: transparent;\r\n    height: 24px;\r\n  }\r\n\r\n  @media ${_styles_device__WEBPACK_IMPORTED_MODULE_2__.device.tablet} {\r\n    grid-template-columns: repeat(8, 1fr);\r\n    padding-top: 80px;\r\n    padding-bottom: 80px;\r\n    .footer-mission {\r\n      grid-column: 1 / span 6;\r\n      grid-row: 1;\r\n      font-size: 48px;\r\n      line-height: 48px;\r\n      margin: 0;\r\n    }\r\n\r\n    .footer-links {\r\n      grid-column: 11 / span 2;\r\n    }\r\n  }\r\n\r\n  @media ${_styles_device__WEBPACK_IMPORTED_MODULE_2__.device.desktop} {\r\n    grid-template-columns: repeat(12, 1fr);\r\n    padding-top: 80px;\r\n    padding-bottom: 80px;\r\n    .footer-mission {\r\n      grid-column: 1 / span 6;\r\n      font-size: 48px;\r\n      line-height: 48px;\r\n    }\r\n\r\n    .footer-links {\r\n      grid-column: 11 / span 2;\r\n    }\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Footer.js\n");

/***/ }),

/***/ "./components/layout/Header/HeaderDropDown.js":
/*!****************************************************!*\
  !*** ./components/layout/Header/HeaderDropDown.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeaderDropDown)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var context_HeaderContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! context/HeaderContext */ \"./context/HeaderContext.js\");\n/* harmony import */ var context_CoreDataContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! context/CoreDataContext */ \"./context/CoreDataContext.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var utils_list_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! utils/list.utils */ \"./utils/list.utils.js\");\n\n\n\n\n\n\n\nfunction HeaderDropDown() {\n    const { blogs, podcasts } = (0,context_CoreDataContext__WEBPACK_IMPORTED_MODULE_3__.useCoreData)();\n    const { headerState } = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(context_HeaderContext__WEBPACK_IMPORTED_MODULE_2__.HeaderContext);\n    const items = {\n        blogs: blogs.map((blog)=>({\n                name: blog.blogger.fullName,\n                slug: blog.slug,\n                lastName: blog.blogger.lastName\n            })).sort((0,utils_list_utils__WEBPACK_IMPORTED_MODULE_6__.dynamicSort)(\"lastName\")),\n        podcasts: podcasts.sort((0,utils_list_utils__WEBPACK_IMPORTED_MODULE_6__.dynamicSort)(\"name\"))\n    };\n    if (!items.blogs || !items.podcasts) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        show: headerState.dropDownOpen,\n        blog: headerState.dropDownKey === \"blogs\",\n        podcast: headerState.dropDownKey === \"podcasts\",\n        podcastHeight: items.podcasts.length * 34 + 80,\n        blogHeight: items.blogs.length * 34 + 80,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"dd-blogs\",\n                children: items.blogs.map((item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                            href: `/blog/${item.slug}`,\n                            children: item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderDropDown.js\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    }, key, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderDropDown.js\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderDropDown.js\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"dd-podcasts\",\n                children: items.podcasts.map((item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                            href: `/podcasts/${item.slug}`,\n                            children: item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderDropDown.js\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this)\n                    }, key, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderDropDown.js\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderDropDown.js\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderDropDown.js\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\nconst Wrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"HeaderDropDown__Wrapper\",\n    componentId: \"sc-ff9b963e-0\"\n})`\r\n  position: relative;\r\n  margin-top: -1px;\r\n  width: 100%;\r\n  height: ${(p)=>p.show ? p.podcast ? p.podcastHeight : p.blogHeight : 0}px;\r\n  //background: linear-gradient(56.8deg, #081921 18.37%, rgba(8, 25, 33, 0.8) 100.63%);\r\n  background-color: var(--blue-dark);\r\n  transition: all 350ms ease-out;\r\n  overflow: hidden;\r\n  z-index: 2;\r\n\r\n  //backdrop-filter: blur(12px);\r\n  //\r\n  //-webkit-backface-visibility: hidden; // Safari shit here\r\n  //-moz-backface-visibility: hidden; // Safari shit here\r\n  //-webkit-transform: translate3d(0, 0, 0); // Safari shit here\r\n  //-moz-transform: translate3d(0, 0, 0); // Safari shit here\r\n\r\n  .dd-podcasts {\r\n    margin: 0 0 0 calc(100% - (445px + var(--border-space)));\r\n    &:before {\r\n      top: ${(p)=>p.show && p.podcast ? -10 : -24}px;\r\n    }\r\n    li {\r\n      opacity: ${(p)=>p.blog ? 0 : 1};\r\n    }\r\n  }\r\n\r\n  .dd-blogs {\r\n    margin: 0 0 0 calc(100% - (727px + var(--border-space)));\r\n    &:before {\r\n      top: ${(p)=>p.show && p.blog ? -10 : -24}px;\r\n    }\r\n    li {\r\n      opacity: ${(p)=>p.blog ? 1 : 0};\r\n    }\r\n  }\r\n\r\n  ul {\r\n    position: absolute;\r\n    padding: 28px 0;\r\n\r\n    &:before {\r\n      position: absolute;\r\n      left: 5px;\r\n      content: \"\";\r\n      display: block;\r\n      width: 20px;\r\n      height: 20px;\r\n      background-color: var(--soft-white);\r\n      transform: rotate(45deg);\r\n      transition: all 350ms ease-out;\r\n    }\r\n  }\r\n\r\n  li {\r\n    font-family: \"Switzer\", sans-serif;\r\n    font-size: 16px;\r\n    margin-top: 14px;\r\n    color: #F9F1E6;\r\n    list-style: none;\r\n    transition: opacity 250ms ease-out;\r\n\r\n    &:hover {\r\n      color: var(--c-brand-lighter);\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Header/HeaderDropDown.js\n");

/***/ }),

/***/ "./components/layout/Header/HeaderMenu/Blogs.js":
/*!******************************************************!*\
  !*** ./components/layout/Header/HeaderMenu/Blogs.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Blogs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var context_HeaderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! context/HeaderContext */ \"./context/HeaderContext.js\");\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var _utils_list_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../utils/list.utils */ \"./utils/list.utils.js\");\n\n\n\n\n\n\n\nfunction Blogs({ title, data }) {\n    const { toggleMenu } = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(context_HeaderContext__WEBPACK_IMPORTED_MODULE_4__.HeaderContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        \"aria-label\": title,\n        children: data.sort((0,_utils_list_utils__WEBPACK_IMPORTED_MODULE_6__.dynamicSort)(\"lastName\")).map((item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: item.route,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        onClick: ()=>toggleMenu(),\n                        children: item.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Blogs.js\",\n                        lineNumber: 18,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Blogs.js\",\n                    lineNumber: 17,\n                    columnNumber: 11\n                }, this)\n            }, key, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Blogs.js\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Blogs.js\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nconst Wrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().ul.withConfig({\n    displayName: \"Blogs__Wrapper\",\n    componentId: \"sc-918972e8-0\"\n})`\r\n  grid-row: 2;\r\n  grid-column: 1/3;\r\n  margin-bottom: 64px;\r\n  padding: 0;\r\n\r\n  :before {\r\n    display: block;\r\n    position: relative;\r\n    content: attr(aria-label);\r\n    font-family: \"Switzer\", serif;\r\n    text-transform: uppercase;\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    margin-bottom: 24px;\r\n    color: var(--c-cream-A80);\r\n  }\r\n\r\n  li {\r\n    list-style: none;\r\n    margin-top: -1px;\r\n    border-top: 1px solid #1C2E33;\r\n    border-bottom: 1px solid #1C2E33;\r\n  }\r\n\r\n  p {\r\n    font-family: \"Switzer\", serif;\r\n    font-size: 16px;\r\n    line-height: 24px;\r\n    font-weight: 400;\r\n    letter-spacing: 0.5px;\r\n    color: var(--c-cream);\r\n\r\n    &:before {\r\n      margin-right: 24px;\r\n      left: 0;\r\n      top: 0;\r\n      content: \"\";\r\n      display: inline-block;\r\n      width: 14px;\r\n      height: 14px;\r\n      background-color: var(--c-cream-A20);\r\n\r\n      border-radius: 14px;\r\n    }\r\n\r\n    &:hover {\r\n      cursor: pointer;\r\n\r\n      &:before {\r\n        background-color: #fa7050;\r\n      }\r\n    }\r\n  }\r\n\r\n  @media ${styles_device__WEBPACK_IMPORTED_MODULE_5__.device.desktop} {\r\n    width: 110%;\r\n    li {\r\n      width: 110%;\r\n    }\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Header/HeaderMenu/Blogs.js\n");

/***/ }),

/***/ "./components/layout/Header/HeaderMenu/Vocations.js":
/*!**********************************************************!*\
  !*** ./components/layout/Header/HeaderMenu/Vocations.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Blogs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var components_svg_chevron_down__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/svg/chevron-down */ \"./components/svg/chevron-down.js\");\n/* harmony import */ var context_HeaderContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! context/HeaderContext */ \"./context/HeaderContext.js\");\n/* harmony import */ var _styles_device__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../styles/device */ \"./styles/device.js\");\n\n\n\n\n\n\n\nfunction Blogs({ title, data, row }) {\n    const [openedList, setOpenedList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const { toggleMenu } = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(context_HeaderContext__WEBPACK_IMPORTED_MODULE_5__.HeaderContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        \"aria-label\": title,\n        row: row,\n        children: data.map((voc, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                className: \"parent-list\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListRow, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: voc.route,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    onClick: ()=>toggleMenu(),\n                                    children: voc.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n                                    lineNumber: 21,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setOpenedList(voc.name === openedList ? null : voc.name),\n                                className: \"chevron-icon\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_svg_chevron_down__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n                                    lineNumber: 26,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HiddenList, {\n                        className: \"hidden-list\",\n                        itemCount: voc.children.length,\n                        show: openedList !== `${voc.name}`,\n                        children: voc.children.map((child, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListRow, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: child.route,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            onClick: ()=>toggleMenu(),\n                                            children: child.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n                                            lineNumber: 36,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n                                        lineNumber: 35,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n                                    lineNumber: 34,\n                                    columnNumber: 17\n                                }, this)\n                            }, \"hidden-list-\" + key, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, key, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\Vocations.js\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nconst Wrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().ul.withConfig({\n    displayName: \"Vocations__Wrapper\",\n    componentId: \"sc-421560ba-0\"\n})`\r\n  grid-row: 1;\r\n  grid-column: 1/3;\r\n  margin-bottom: 64px;\r\n  padding: 0;\r\n  list-style: none;\r\n\r\n  :before{\r\n    display: block;\r\n    position: relative;\r\n    content:attr(aria-label);\r\n    font-family: \"Switzer\", serif;\r\n    text-transform: uppercase;\r\n    font-size: 14px;\r\n    letter-spacing: 0.5px;\r\n    font-weight: 400;\r\n    margin-bottom: 24px;\r\n    color: var(--c-cream-A40);\r\n  }\r\n\r\n  .li {\r\n    list-style: none;\r\n  }\r\n`;\nconst ListRow = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"Vocations__ListRow\",\n    componentId: \"sc-421560ba-1\"\n})`\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-top: -1px;\r\n  border-top: 1px solid #1C2E33;\r\n  border-bottom: 1px solid #1C2E33;\r\n\r\n  p {\r\n    font-family: \"Switzer\", \"Helvetica Neue\", Helvetica, sans-serif;\r\n    font-size: 16px;\r\n    line-height: 24px;\r\n    font-weight: 400;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    color: var(--c-cream);\r\n\r\n    &:before {\r\n      margin-right: 24px;\r\n      left: 0;\r\n      top: 0;\r\n      content: \"\";\r\n      display: inline-block;\r\n      width: 14px;\r\n      height: 14px;\r\n      background-color: var(--c-cream-A20);\r\n      border-radius: 14px;\r\n    }\r\n\r\n    &:hover {\r\n      cursor: pointer;\r\n\r\n      &:before {\r\n        background-color: #fa7051;\r\n      }\r\n    }\r\n  }\r\n\r\n  .chevron-icon {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    height: 48px;\r\n    width: 32px;\r\n    border-radius: 26px;\r\n\r\n    svg {\r\n      width: 14px;\r\n      height: 14px;\r\n\r\n      path {\r\n        stroke: var(--c-cream);\r\n      }\r\n    }\r\n\r\n    &:hover {\r\n      cursor: pointer;\r\n\r\n      path {\r\n        stroke: #fa7051;\r\n        stroke-width: 2.4;\r\n      }\r\n    }\r\n  }\r\n  \r\n  @media ${_styles_device__WEBPACK_IMPORTED_MODULE_6__.device.desktop} {\r\n    width: 110%;\r\n  }\r\n`;\nconst HiddenList = styled_components__WEBPACK_IMPORTED_MODULE_1___default().ul.withConfig({\n    displayName: \"Vocations__HiddenList\",\n    componentId: \"sc-421560ba-2\"\n})`\r\n  height: ${(props)=>props.show ? 0 : props.itemCount * 57}px;\r\n  transition: 350ms ease-in-out;\r\n  padding-left: 38px;\r\n  overflow: hidden;\r\n  list-style: none;\r\n  @media ${_styles_device__WEBPACK_IMPORTED_MODULE_6__.device.desktop} {\r\n    width: 110%;\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Header/HeaderMenu/Vocations.js\n");

/***/ }),

/***/ "./components/layout/Header/HeaderMenu/index.js":
/*!******************************************************!*\
  !*** ./components/layout/Header/HeaderMenu/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeaderMenu)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var context_HeaderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! context/HeaderContext */ \"./context/HeaderContext.js\");\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var context_CoreDataContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! context/CoreDataContext */ \"./context/CoreDataContext.js\");\n/* harmony import */ var _Blogs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Blogs */ \"./components/layout/Header/HeaderMenu/Blogs.js\");\n/* harmony import */ var _Vocations__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Vocations */ \"./components/layout/Header/HeaderMenu/Vocations.js\");\n/* harmony import */ var _shared_Buttons_AnimatedTextButton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../shared/Buttons/AnimatedTextButton */ \"./components/shared/Buttons/AnimatedTextButton.jsx\");\n\n\n\n\n\n\n\n\n\n\nconst mainNavigationItems = [\n    {\n        name: \"Articles\",\n        route: \"/recherche\"\n    },\n    {\n        name: \"Th\\xe8mes\",\n        route: \"/categories\"\n    },\n    {\n        name: \"Formations\",\n        route: \"/formations\"\n    },\n    {\n        name: \"Parcours e-mails\",\n        route: \"/parcours-emails\"\n    },\n    {\n        name: \"Podcasts\",\n        route: \"/podcasts\"\n    },\n    {\n        name: \"Webinaires\",\n        route: \"/webinaires\"\n    }\n];\nconst setChildren = (children)=>{\n    return children.map((child)=>({\n            route: `/categories/${child.type}/${child.slug}`,\n            name: child.name\n        }));\n};\nfunction HeaderMenu() {\n    const { headerState, toggleMenu } = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(context_HeaderContext__WEBPACK_IMPORTED_MODULE_4__.HeaderContext);\n    const { blogs, topicGroups } = (0,context_CoreDataContext__WEBPACK_IMPORTED_MODULE_6__.useCoreData)();\n    const vocationsData = topicGroups.filter((voc)=>voc.children?.length > 0).map((voc)=>({\n            route: `/categories/${voc.type}/${voc.slug}`,\n            name: voc.name,\n            children: setChildren(voc.children)\n        }));\n    const blogsData = blogs.map((blog)=>({\n            route: `/blog/${blog.slug}`,\n            name: blog.blogger.fullName,\n            lastName: blog.blogger.lastName\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Main, {\n        isOpen: headerState.showMenu,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dark-filter\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeftSection, {\n                isOpen: headerState.showMenu,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeftContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: mainNavigationItems.map((item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.route,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeftItem, {\n                                        onClick: ()=>toggleMenu(),\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, this)\n                                }, key, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"menu-buttons\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_Buttons_AnimatedTextButton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                theme: \"light\",\n                                text: \"Nous soutenir\",\n                                link: \"/soutenir\",\n                                onClickFunction: ()=>toggleMenu()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_Buttons_AnimatedTextButton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                theme: \"light\",\n                                text: \"\\xc0 propos\",\n                                link: \"/a-propos\",\n                                onClickFunction: ()=>toggleMenu()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RightSection, {\n                isOpen: headerState.showMenu,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Vocations__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        title: \"vocations\",\n                        data: vocationsData\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Blogs__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        title: \"blogs\",\n                        data: blogsData\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\HeaderMenu\\\\index.js\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\nconst Main = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"HeaderMenu__Main\",\n    componentId: \"sc-f4dd8104-0\"\n})`\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  visibility: ${(p)=>p.isOpen ? \"visible\" : \"hidden\"};\r\n  transition-delay: ${(p)=>p.isOpen ? \"0ms\" : \"600ms\"};\r\n  z-index: 2000;\r\n\r\n\r\n  // Hide Scrollbar\r\n  &::-webkit-scrollbar {\r\n    display: none; /* Safari and Chrome */\r\n  }\r\n\r\n  -ms-overflow-style: none; /* IE and Edge */\r\n  scrollbar-width: none; /* Firefox */\r\n  overflow-y: scroll;\r\n\r\n  .dark-filter {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    height: 100%;\r\n    width: 100%;\r\n    background-color: orangered;\r\n    opacity: ${(p)=>p.isOpen ? \"1\" : \"0\"};\r\n    transition: opacity 800ms;\r\n  }\r\n`;\nconst LeftSection = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"HeaderMenu__LeftSection\",\n    componentId: \"sc-f4dd8104-1\"\n})`\r\n  position: relative;\r\n  background-color: var(--blue-dark);\r\n  width: 100vw;\r\n  height: 100vh;\r\n  padding: 126px var(--border-space) 64px 0;\r\n  clip-path: inset(0 0 ${(p)=>p.isOpen ? \"0%\" : \"100%\"} 0);\r\n  transition: clip-path 600ms cubic-bezier(0.93, 0.03, 0.23, 0.84);\r\n\r\n  @media ${styles_device__WEBPACK_IMPORTED_MODULE_5__.device.desktop} {\r\n    position: absolute;\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    grid-auto-rows: max-content;\r\n    column-gap: 24px;\r\n    padding: 126px 0;\r\n    height: 100vh;\r\n    width: 50%;\r\n    left: 0;\r\n    top: 0;\r\n    clip-path: inset(0 ${(p)=>p.isOpen ? \"0%\" : \"100%\"} 0 0);\r\n\r\n    .menu-buttons {\r\n      grid-column: 1/4;\r\n      margin-top: 64px;\r\n    }\r\n  }\r\n\r\n  .menu-buttons {\r\n    position: relative;\r\n    display: flex;\r\n    flex-direction: row-reverse;\r\n    gap: 16px;\r\n  }\r\n`;\nconst LeftContent = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"HeaderMenu__LeftContent\",\n    componentId: \"sc-f4dd8104-2\"\n})`\r\n  position: relative;\r\n  grid-column: 2/4;\r\n  height: 100%;\r\n  ul {\r\n    padding: 0;\r\n  }\r\n`;\nconst LeftItem = styled_components__WEBPACK_IMPORTED_MODULE_1___default().li.withConfig({\n    displayName: \"HeaderMenu__LeftItem\",\n    componentId: \"sc-f4dd8104-3\"\n})`\r\n  position: relative;\r\n  width: 100%;\r\n  text-align: right;\r\n  list-style: none;\r\n  font-size: 32px;\r\n  margin-top: 20px;\r\n  font-weight: 400;\r\n  color: var(--c-cream);\r\n  white-space: nowrap;\r\n\r\n  @media ${styles_device__WEBPACK_IMPORTED_MODULE_5__.device.tablet} {\r\n    font-size: 40px;\r\n    margin-bottom: 16px;\r\n    &:hover {\r\n      color: var(--c-brand-lighter);\r\n      cursor: pointer;\r\n    }\r\n  }\r\n`;\nconst RightSection = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"HeaderMenu__RightSection\",\n    componentId: \"sc-f4dd8104-4\"\n})`\r\n  position: relative;\r\n  width: 100%;\r\n  background-color: var(--blue-dark);\r\n  padding: 64px var(--border-space);\r\n  clip-path: inset(${(p)=>p.isOpen ? \"0%\" : \"100%\"} 0 0 0);\r\n  transition: clip-path 600ms cubic-bezier(0.93, 0.03, 0.23, 0.84);\r\n\r\n  @media ${styles_device__WEBPACK_IMPORTED_MODULE_5__.device.desktop} {\r\n    position: absolute;\r\n    right: 0;\r\n    top: 0;\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    padding: 126px var(--border-space) 126px 0;\r\n    height: 100vh;\r\n    width: 51%;\r\n    overflow-y: scroll;\r\n    clip-path: inset(0 0 0 ${(p)=>p.isOpen ? \"0%\" : \"100%\"});\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Header/HeaderMenu/index.js\n");

/***/ }),

/***/ "./components/layout/Header/Logo.js":
/*!******************************************!*\
  !*** ./components/layout/Header/Logo.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var context_HeaderContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! context/HeaderContext */ \"./context/HeaderContext.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nfunction Logo({ white }) {\n    const { toggleMenu } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(context_HeaderContext__WEBPACK_IMPORTED_MODULE_2__.HeaderContext);\n    let logoSrc = white === true ? \"/images/tpsg-logo-white.svg\" : \"/images/tpsg-logo.svg\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n        href: \"/\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoWrapper, {\n            onClick: ()=>toggleMenu(true),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                src: logoSrc,\n                alt: \"LOGO_TPSG\",\n                sizes: \"100px\",\n                fill: true\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\Logo.js\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\Logo.js\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\Logo.js\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n// size ratio 2.09\nconst LogoWrapper = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Logo__LogoWrapper\",\n    componentId: \"sc-4bff2d1c-0\"\n})`\r\n  position: relative;\r\n  height: 25px;\r\n  width: calc(25px * 2.09);\r\n  margin-left: var(--border-space);\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Header/Logo.js\n");

/***/ }),

/***/ "./components/layout/Header/MenuButtons.js":
/*!*************************************************!*\
  !*** ./components/layout/Header/MenuButtons.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MenuButtons)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var context_HeaderContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! context/HeaderContext */ \"./context/HeaderContext.js\");\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nfunction MenuButtons({ invert }) {\n    const { headerState, toggleMenu, onDropDownClickOutside } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(context_HeaderContext__WEBPACK_IMPORTED_MODULE_3__.HeaderContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        invert: invert,\n        menuOpen: headerState.showMenu,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                href: \"/recherche\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MenuButton, {\n                    onClick: ()=>{\n                        toggleMenu(true);\n                        onDropDownClickOutside();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SearchIcon, {}, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MenuButton, {\n                onClick: ()=>{\n                    toggleMenu();\n                    onDropDownClickOutside();\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MenuIcon, {}, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\nconst Wrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"MenuButtons__Wrapper\",\n    componentId: \"sc-fcd954af-0\"\n})`\r\n  position: relative;\r\n  z-index: 9999;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 101%;\r\n  \r\n  padding-right: var(--border-space);\r\n  padding-left: 32px;\r\n  flex-direction: row;\r\n  gap: 8px;\r\n  \r\n  //border-bottom: ${(p)=>p.menuOpen ? \"1px solid #39474D\" : \"1px solid transparent\"};\r\n  transition-delay: 300ms;\r\n  \r\n  svg {\r\n    path {\r\n      fill: ${(p)=>p.menuOpen || p.invert ? \"var(--c-cream)\" : \"black\"};\r\n      transition: fill 450ms ease-in-out;\r\n    }\r\n  }\r\n  \r\n  .menu-icon {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: end;\r\n    gap: ${(p)=>p.menuOpen ? 5 : 3}px;\r\n    transition: all 250ms ease-in-out;\r\n    width: 32px;\r\n    hr {\r\n      transform-origin: right;\r\n      color: ${(p)=>p.menuOpen || p.invert ? \"var(--c-cream)\" : \"black\"};\r\n      border: 1.2px solid ${(p)=>p.menuOpen || p.invert ? \"var(--c-cream)\" : \"black\"};\r\n      margin: 0;\r\n      transition: all 250ms ease-in-out;\r\n    }\r\n    hr:nth-child(1) {\r\n      width: ${(p)=>p.menuOpen ? 24 : 27}px;\r\n      transform: ${(p)=>p.menuOpen ? \"rotate(-35deg)\" : \"rotate(0)\"};\r\n    }\r\n    hr:nth-child(2){\r\n      width: ${(p)=>p.menuOpen ? 0 : 21}px;\r\n      opacity: ${(p)=>p.menuOpen ? 0 : 1};\r\n    }\r\n    hr:nth-child(3){\r\n      width: 24px;\r\n      transform: ${(p)=>p.menuOpen ? \"rotate(35deg)\" : \"rotate(0)\"};\r\n    }\r\n  }\r\n  \r\n  @media ${styles_device__WEBPACK_IMPORTED_MODULE_4__.device.desktop} {\r\n    //margin-right: 15px;\r\n  }\r\n`;\nconst MenuButton = styled_components__WEBPACK_IMPORTED_MODULE_1___default().button.withConfig({\n    displayName: \"MenuButtons__MenuButton\",\n    componentId: \"sc-fcd954af-1\"\n})`\r\n  outline: none;\r\n  border: none;\r\n  display: block;\r\n  background-color: transparent;\r\n  cursor: pointer;\r\n  height: 48px;\r\n  width: 48px;\r\n  \r\n  &:hover {\r\n    * {\r\n      path {\r\n        fill: var(--brand-color);\r\n      }\r\n    }\r\n    * {\r\n      hr {\r\n        color: var(--brand-color);\r\n        border-color: var(--brand-color);\r\n      }\r\n    }\r\n  }\r\n`;\nconst SearchIcon = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"20\",\n        height: \"19\",\n        viewBox: \"0 0 20 19\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M15.2104 8.31546C15.2104 11.7473 12.4283 14.5294 8.99641 14.5294C5.56455 14.5294 2.78247 11.7473 2.78247 8.31546C2.78247 4.8836 5.56455 2.10152 8.99641 2.10152C12.4283 2.10152 15.2104 4.8836 15.2104 8.31546ZM14.0588 14.7844C12.6637 15.8776 10.9062 16.5294 8.99641 16.5294C4.45998 16.5294 0.782471 12.8519 0.782471 8.31546C0.782471 3.77903 4.45998 0.101524 8.99641 0.101524C13.5328 0.101524 17.2104 3.77903 17.2104 8.31546C17.2104 10.2213 16.5612 11.9756 15.4721 13.3693L19.6182 17.5154L18.204 18.9296L14.0588 14.7844Z\",\n            fill: \"#161616\"\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, undefined);\n};\nconst MenuIcon = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"menu-icon\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n                lineNumber: 118,\n                columnNumber: 34\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n                lineNumber: 118,\n                columnNumber: 39\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n                lineNumber: 118,\n                columnNumber: 44\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\MenuButtons.js\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Header/MenuButtons.js\n");

/***/ }),

/***/ "./components/layout/Header/NavigationBar.js":
/*!***************************************************!*\
  !*** ./components/layout/Header/NavigationBar.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Logo */ \"./components/layout/Header/Logo.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var context_HeaderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! context/HeaderContext */ \"./context/HeaderContext.js\");\n/* harmony import */ var _MenuButtons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MenuButtons */ \"./components/layout/Header/MenuButtons.js\");\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n\n\n\n\n\n\n\n\nfunction NavigationBar({ invert }) {\n    const ddRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const { headerState, onDropDownButtonClick, onDropDownClickOutside } = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(context_HeaderContext__WEBPACK_IMPORTED_MODULE_4__.HeaderContext);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (ddRef.current && !ddRef.current.contains(event.target)) {\n                if (headerState.dropDownOpen) {\n                    onDropDownClickOutside();\n                    event.stopPropagation();\n                }\n            }\n        };\n        document && document.addEventListener(\"click\", handleClickOutside, true);\n        return ()=>{\n            document && document.removeEventListener(\"click\", handleClickOutside, true);\n        };\n    }, [\n        headerState\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        menuOpen: headerState.showMenu,\n        invert: invert,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                className: \"animated-buttons-line\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                white: invert\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RightNav, {\n                invert: invert,\n                ref: ddRef,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                onClick: ()=>onDropDownButtonClick(\"blogs\"),\n                                children: \"Blogs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                onClick: ()=>onDropDownClickOutside(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                    href: \"/categories\",\n                                    children: \"Th\\xe8mes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                                    lineNumber: 41,\n                                    columnNumber: 57\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                onClick: ()=>onDropDownClickOutside(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                    href: \"/formations\",\n                                    children: \"Formations\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                                    lineNumber: 42,\n                                    columnNumber: 57\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                onClick: ()=>onDropDownButtonClick(\"podcasts\"),\n                                children: \"Podcasts\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                onClick: ()=>onDropDownClickOutside(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                    href: \"/webinaires\",\n                                    children: \"Webinaires\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                                    lineNumber: 44,\n                                    columnNumber: 57\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                onClick: ()=>onDropDownClickOutside(),\n                                className: \"highlight-top-menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                    href: \"/soutenir\",\n                                    children: \"Soutenir\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                                    lineNumber: 45,\n                                    columnNumber: 90\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"nav-v-separator\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MenuButtons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        invert: invert\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animated-background\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\NavigationBar.js\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\nconst Wrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"NavigationBar__Wrapper\",\n    componentId: \"sc-bc67f75e-0\"\n})`\r\n  display: flex;\r\n  width: 100%;\r\n  height: 80px;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  border-bottom: 1px solid rgba(0,0,0,0.20);\r\n\r\n  .animated-buttons-line {\r\n    position: absolute;\r\n    right: 0;\r\n    top: 70px;\r\n    z-index: 2100;\r\n    border: ${(p)=>p.menuOpen ? \"1px solid #1C2E33\" : \"1px solid transparent\"};\r\n    width: 100%;\r\n    transition: all 600ms ease-in-out;\r\n\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_6__.device.desktop} {\r\n      right: 15px;\r\n      width: ${(p)=>p.menuOpen ? \"calc(var(--border-space) + 98px)\" : \"0\"};\r\n    }\r\n  }\r\n  \r\n  .animated-background {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    height: 100%;\r\n    width: 100%;\r\n    background-color: var(--blue-dark);\r\n    transform: ${(p)=>p.menuOpen ? \"translateY(0)\" : \"translateY(-100%)\"};\r\n    transition: transform 450ms ease-in-out;\r\n    z-index: 2100;\r\n\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_6__.device.desktop} {\r\n      display: none;\r\n    }\r\n  }\r\n  \r\n\r\n`;\nconst RightNav = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"NavigationBar__RightNav\",\n    componentId: \"sc-bc67f75e-1\"\n})`\r\n\r\n  position: relative;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  font-family: \"Switzer\", \"Helvetica Neue\", Helvetica, sans-serif;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n\r\n  ul li {\r\n    list-style: none;\r\n    display: none;\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_6__.device.desktop} {\r\n      display: inline;\r\n    }\r\n  }\r\n  li {\r\n    margin-left: 32px;\r\n    cursor: pointer;\r\n    color: ${(p)=>p.invert ? \"var(--soft-white)\" : \"black\"};\r\n  }\r\n  \r\n  .highlight-top-menu {\r\n    color: ${(p)=>p.invert ? \"var(--soft-white)\" : \"var(--brand-color)\"};\r\n  }\r\n  \r\n  .nav-v-separator {\r\n    height: 100%;\r\n    margin: 0 0 0 32px;\r\n    width: 0;\r\n    border-left: 1px solid rgba(0,0,0,0.2);\r\n  }\r\n  \r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Header/NavigationBar.js\n");

/***/ }),

/***/ "./components/layout/Header/index.js":
/*!*******************************************!*\
  !*** ./components/layout/Header/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var context_HeaderContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! context/HeaderContext */ \"./context/HeaderContext.js\");\n/* harmony import */ var _NavigationBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NavigationBar */ \"./components/layout/Header/NavigationBar.js\");\n/* harmony import */ var _HeaderDropDown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./HeaderDropDown */ \"./components/layout/Header/HeaderDropDown.js\");\n/* harmony import */ var _HeaderMenu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./HeaderMenu */ \"./components/layout/Header/HeaderMenu/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n\n\n\n\n\n\n\n\nfunction Header() {\n    const { pathname } = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [invert, setInvert] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let p = pathname;\n        if (p.includes(\"ressources\")) {\n            setInvert(false);\n            return;\n        }\n        if (p.includes(\"/[page]\") || p.includes(\"[vocation]\") || p.includes(\"[ministry]\") || p.includes(\"[]\")) {\n            setInvert(true);\n        } else {\n            setInvert(false);\n        }\n    }, [\n        pathname\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(context_HeaderContext__WEBPACK_IMPORTED_MODULE_3__.HeaderProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n            invert: invert,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavigationBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    invert: invert\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\index.js\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HeaderDropDown__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\index.js\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HeaderMenu__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\index.js\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\index.js\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\Header\\\\index.js\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nconst Wrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"Header__Wrapper\",\n    componentId: \"sc-6f0c4967-0\"\n})`\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100vw;\r\n  z-index: 1400;\r\n  background-color: \"transparent\";\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Header/index.js\n");

/***/ }),

/***/ "./components/layout/index.js":
/*!************************************!*\
  !*** ./components/layout/index.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var context_CoreDataContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! context/CoreDataContext */ \"./context/CoreDataContext.js\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Header */ \"./components/layout/Header/index.js\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Footer */ \"./components/layout/Footer.js\");\n/* harmony import */ var _Popup__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Popup */ \"./components/Popup/index.js\");\n/* harmony import */ var _CookieBanner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../CookieBanner */ \"./components/CookieBanner.js\");\n\n\n\n\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(context_CoreDataContext__WEBPACK_IMPORTED_MODULE_1__.CoreDataProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\index.js\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\index.js\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CookieBanner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\index.js\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Popup__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\index.js\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\layout\\\\index.js\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2xheW91dC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBMEQ7QUFDN0I7QUFDQTtBQUNEO0FBQ2M7QUFHM0IsU0FBU0ssT0FBTyxFQUFFQyxRQUFRLEVBQUU7SUFDekMscUJBQ0UsOERBQUNOLHFFQUFnQkE7OzBCQUNmLDhEQUFDQywrQ0FBTUE7Ozs7O1lBQ05LOzBCQUNELDhEQUFDSiwrQ0FBTUE7Ozs7OzBCQUNQLDhEQUFDRSxxREFBWUE7Ozs7OzBCQUNiLDhEQUFDRCw4Q0FBS0E7Ozs7Ozs7Ozs7O0FBR1oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90cHNnLW5leHQvLi9jb21wb25lbnRzL2xheW91dC9pbmRleC5qcz9kYjI4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENvcmVEYXRhUHJvdmlkZXIgfSBmcm9tIFwiY29udGV4dC9Db3JlRGF0YUNvbnRleHRcIlxyXG5pbXBvcnQgSGVhZGVyIGZyb20gXCIuL0hlYWRlclwiXHJcbmltcG9ydCBGb290ZXIgZnJvbSBcIi4vRm9vdGVyXCJcclxuaW1wb3J0IFBvcHVwIGZyb20gXCIuLi9Qb3B1cFwiXHJcbmltcG9ydCBDb29raWVCYW5uZXIgZnJvbSBcIi4uL0Nvb2tpZUJhbm5lclwiXHJcblxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0KHsgY2hpbGRyZW4gfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8Q29yZURhdGFQcm92aWRlcj5cclxuICAgICAgPEhlYWRlci8+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgPEZvb3Rlci8+XHJcbiAgICAgIDxDb29raWVCYW5uZXIvPlxyXG4gICAgICA8UG9wdXAvPlxyXG4gICAgPC9Db3JlRGF0YVByb3ZpZGVyPlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsiQ29yZURhdGFQcm92aWRlciIsIkhlYWRlciIsIkZvb3RlciIsIlBvcHVwIiwiQ29va2llQmFubmVyIiwiTGF5b3V0IiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/layout/index.js\n");

/***/ }),

/***/ "./components/shared/Buttons/AnimatedArrowButton.jsx":
/*!***********************************************************!*\
  !*** ./components/shared/Buttons/AnimatedArrowButton.jsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnimatedArrowButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var components_svg_long_arrow__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/svg/long-arrow */ \"./components/svg/long-arrow.js\");\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n\n\n\n\n/**\r\n *\r\n * @param direction\r\n * @param theme \"light\" or \"dark\"\r\n * @param onClickFunction (optionnel)\r\n * @return {JSX.Element}\r\n * @constructor\r\n */ function AnimatedArrowButton({ reverse, onClickFunction, theme, disabled }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ButtonWrapper, {\n        theme: theme,\n        reverse: reverse,\n        disabled: disabled,\n        onClick: (e)=>onClickFunction(e),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_svg_long_arrow__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Buttons\\\\AnimatedArrowButton.jsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_svg_long_arrow__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Buttons\\\\AnimatedArrowButton.jsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Buttons\\\\AnimatedArrowButton.jsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nconst ButtonWrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().button.withConfig({\n    displayName: \"AnimatedArrowButton__ButtonWrapper\",\n    componentId: \"sc-e439b357-0\"\n})`\r\n  padding: 0;\r\n  position: relative;\r\n  border-radius: 32px;\r\n  background-color: transparent;\r\n  border: 1px solid ${(p)=>p.theme === \"dark\" ? \"var(--c-soft-cream)\" : \"#161616\"};\r\n\r\n  color: ${(p)=>p.theme === \"dark\" ? \"var(--c-soft-cream)\" : \"#161616\"};\r\n\r\n  overflow: hidden;\r\n  box-sizing: content-box;\r\n  height: 32px;\r\n  width: 48px;\r\n  \r\n  opacity: ${(p)=>p.disabled ? 0.5 : 1};\r\n\r\n  isolation: isolate;\r\n\r\n  .icn-arrow {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    height: 100%;\r\n    width: 100%;\r\n    border-radius: 32px;\r\n  }\r\n\r\n  .icn-arrow:first-child {\r\n    transform: rotate(${(p)=>p.reverse ? \"180deg\" : \"0\"});\r\n\r\n    * {\r\n      fill: ${(p)=>p.theme === \"dark\" ? \"var(--c-soft-cream)\" : \"var(--soft-dark)\"};\r\n    }\r\n  }\r\n\r\n  .icn-arrow:last-child {\r\n    position: absolute;\r\n    top: 0;\r\n    background-color: ${(p)=>p.theme === \"dark\" ? \"var(--c-soft-cream)\" : \"var(--soft-dark)\"};\r\n    transform: rotate(${(p)=>p.reverse ? \"180deg\" : \"0\"}) translateX(-100%);\r\n\r\n    transition: transform 350ms ease-out;\r\n\r\n    * {\r\n      fill: ${(p)=>p.theme === \"dark\" ? \"var(--soft-dark)\" : \"var(--c-soft-cream)\"};\r\n    }\r\n  }\r\n\r\n  &:active {\r\n    border-color: var(--brand-color);\r\n    * {\r\n      fill: var(--brand-color);\r\n    }\r\n  }\r\n\r\n  @media ${styles_device__WEBPACK_IMPORTED_MODULE_3__.device.desktop} {\r\n    &:active {\r\n      border-color: ${(p)=>p.theme === \"dark\" ? \"var(--c-soft-cream)\" : \"var(--soft-dark)\"};\r\n\r\n      * {\r\n        fill: black;\r\n      }\r\n    }\r\n\r\n    &:hover {\r\n      .icn-arrow:last-child {\r\n        transform: rotate(${(p)=>p.reverse ? \"180deg\" : \"0\"}) translateX(${(p)=>!p.disabled ? \"0\" : \"-100%\"});\r\n      }\r\n\r\n      cursor: ${(p)=>p.disabled ? \"default\" : \"pointer\"};\r\n    }\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/Buttons/AnimatedArrowButton.jsx\n");

/***/ }),

/***/ "./components/shared/Buttons/AnimatedTextButton.jsx":
/*!**********************************************************!*\
  !*** ./components/shared/Buttons/AnimatedTextButton.jsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnimatedTextButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var components_shared_CondLink__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/shared/CondLink */ \"./components/shared/CondLink.js\");\n\n\n\n/**\r\n *\r\n * @param text\r\n * @param link (optionnel)\r\n * @param onClickFunction (optionnel)\r\n * @param theme \"light\" ou \"dark\"\r\n * @return {JSX.Element}\r\n * @constructor\r\n */ function AnimatedTextButton({ text, link, onClickFunction, theme }) {\n    function Button() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ButtonWrapper, {\n            className: \"animated-text-button\",\n            text: text,\n            onClick: onClickFunction ? ()=>onClickFunction() : null,\n            light: theme === \"light\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Buttons\\\\AnimatedTextButton.jsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Buttons\\\\AnimatedTextButton.jsx\",\n            lineNumber: 16,\n            columnNumber: 12\n        }, this);\n    }\n    if (link) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_shared_CondLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            link: link,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {}, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Buttons\\\\AnimatedTextButton.jsx\",\n                lineNumber: 26,\n                columnNumber: 34\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Buttons\\\\AnimatedTextButton.jsx\",\n            lineNumber: 26,\n            columnNumber: 12\n        }, this);\n    } else {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {}, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Buttons\\\\AnimatedTextButton.jsx\",\n            lineNumber: 28,\n            columnNumber: 12\n        }, this);\n    }\n}\nconst ButtonWrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().button.withConfig({\n    displayName: \"AnimatedTextButton__ButtonWrapper\",\n    componentId: \"sc-9eb99ee0-0\"\n})`\r\n  position: relative;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 0 24px 0 24px;\r\n  background-color: transparent;\r\n  border: 1px solid ${(p)=>p.light ? \"var(--c-soft-cream)\" : \"black\"};\r\n  border-radius: 100px;\r\n  font-family: Switzer, serif;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  overflow: hidden;\r\n  isolation: isolate;\r\n  line-height: 32px;\r\n\r\n  p {\r\n    color: ${(p)=>p.light ? \"var(--c-soft-cream)\" : \"black\"};\r\n    margin: 0;\r\n    height: 100%;\r\n    transition: transform 350ms ease-out;\r\n  }\r\n  \r\n  &:after {\r\n    position: absolute;\r\n    content: '${(props)=>props.text}';\r\n    color: ${(p)=>p.light ? \"#081921\" : \"var(--c-soft-cream)\"};\r\n    background-color: ${(p)=>p.light ? \"var(--c-soft-cream)\" : \"black\"};\r\n    border-radius: 100px;\r\n    top: 100%;\r\n    height: 100%;\r\n    width: 100%;\r\n    transition: transform 350ms ease-out;\r\n  }\r\n  \r\n  &:hover {\r\n    p {\r\n      transform: translateY(-100%);\r\n    }\r\n    &:after {\r\n      transform: translateY(-100%);\r\n    }\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/Buttons/AnimatedTextButton.jsx\n");

/***/ }),

/***/ "./components/shared/Buttons/ButtonClose.js":
/*!**************************************************!*\
  !*** ./components/shared/Buttons/ButtonClose.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ButtonClose)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n\n\n\nfunction ButtonClose({ handleClick }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        onClick: handleClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            children: \"+\"\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Buttons\\\\ButtonClose.js\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Buttons\\\\ButtonClose.js\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\nconst Wrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"ButtonClose__Wrapper\",\n    componentId: \"sc-44a20ba0-0\"\n})`\r\n\t\t\r\n\t\theight: 28px;\r\n\t\tborder-radius: 16px;\r\n\t\tpadding: 1px 22px 0 18px;\r\n\t\tbackground-color: var(--c-soft-cream);\r\n\t\t\r\n\t\tcursor: pointer;\r\n\t\t\r\n\t\tp {\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 32px;\r\n\t\t\t\ttransform: rotate(45deg);\r\n\t\t\t\tcolor: black;\r\n\t\t}\r\n\t\t\r\n\t\t@media ${styles_device__WEBPACK_IMPORTED_MODULE_2__.device.desktop} {\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\t\tbackground-color: var(--c-brand-lighter);\r\n\t\t\t\t\t\tp {\r\n\t\t\t\t\t\t\t\tcolor: var(--c-soft-cream);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t}\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3NoYXJlZC9CdXR0b25zL0J1dHRvbkNsb3NlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBdUM7QUFDQTtBQUd4QixTQUFTRSxZQUFZLEVBQUVDLFdBQVcsRUFBRTtJQUVqRCxxQkFDRSw4REFBQ0M7UUFBUUMsU0FBU0Y7a0JBQ2hCLDRFQUFDRztzQkFBRTs7Ozs7Ozs7Ozs7QUFHVDtBQUVBLE1BQU1GLFVBQVVKLDREQUFVOzs7RUFBQSxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7OztTQWlCbEIsRUFBRUMsaURBQU1BLENBQUNPLE9BQU8sQ0FBQzs7Ozs7Ozs7QUFRMUIsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Rwc2ctbmV4dC8uL2NvbXBvbmVudHMvc2hhcmVkL0J1dHRvbnMvQnV0dG9uQ2xvc2UuanM/MTFkMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgc3R5bGVkIGZyb20gXCJzdHlsZWQtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyBkZXZpY2UgfSBmcm9tIFwic3R5bGVzL2RldmljZVwiO1xyXG5cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEJ1dHRvbkNsb3NlKHsgaGFuZGxlQ2xpY2sgfSkge1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPFdyYXBwZXIgb25DbGljaz17aGFuZGxlQ2xpY2t9PlxyXG5cdFx0ICAgIDxwPis8L3A+XHJcbiAgICA8L1dyYXBwZXI+XHJcbiAgKVxyXG59XHJcblxyXG5jb25zdCBXcmFwcGVyID0gc3R5bGVkLmRpdmBcclxuXHRcdFxyXG5cdFx0aGVpZ2h0OiAyOHB4O1xyXG5cdFx0Ym9yZGVyLXJhZGl1czogMTZweDtcclxuXHRcdHBhZGRpbmc6IDFweCAyMnB4IDAgMThweDtcclxuXHRcdGJhY2tncm91bmQtY29sb3I6IHZhcigtLWMtc29mdC1jcmVhbSk7XHJcblx0XHRcclxuXHRcdGN1cnNvcjogcG9pbnRlcjtcclxuXHRcdFxyXG5cdFx0cCB7XHJcblx0XHRcdFx0bWFyZ2luOiAwO1xyXG5cdFx0XHRcdGZvbnQtc2l6ZTogMjRweDtcclxuXHRcdFx0XHRsaW5lLWhlaWdodDogMzJweDtcclxuXHRcdFx0XHR0cmFuc2Zvcm06IHJvdGF0ZSg0NWRlZyk7XHJcblx0XHRcdFx0Y29sb3I6IGJsYWNrO1xyXG5cdFx0fVxyXG5cdFx0XHJcblx0XHRAbWVkaWEgJHtkZXZpY2UuZGVza3RvcH0ge1xyXG5cdFx0XHRcdCY6aG92ZXIge1xyXG5cdFx0XHRcdFx0XHRiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1jLWJyYW5kLWxpZ2h0ZXIpO1xyXG5cdFx0XHRcdFx0XHRwIHtcclxuXHRcdFx0XHRcdFx0XHRcdGNvbG9yOiB2YXIoLS1jLXNvZnQtY3JlYW0pO1xyXG5cdFx0XHRcdFx0XHR9XHJcblx0XHRcdFx0fVxyXG5cdFx0fVxyXG5gOyJdLCJuYW1lcyI6WyJzdHlsZWQiLCJkZXZpY2UiLCJCdXR0b25DbG9zZSIsImhhbmRsZUNsaWNrIiwiV3JhcHBlciIsIm9uQ2xpY2siLCJwIiwiZGl2IiwiZGVza3RvcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/shared/Buttons/ButtonClose.js\n");

/***/ }),

/***/ "./components/shared/Buttons/SmallButton.jsx":
/*!***************************************************!*\
  !*** ./components/shared/Buttons/SmallButton.jsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmallButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction SmallButton({ text, action, theme = \"light\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StlSmallButton, {\n        onClick: action,\n        className: theme,\n        children: text\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Buttons\\\\SmallButton.jsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\nconst StlSmallButton = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"SmallButton__StlSmallButton\",\n    componentId: \"sc-1dc5b33c-0\"\n})`\r\n  padding: 6px 20px;\r\n  font-family: Switzer, sans-serif;\r\n  font-weight: 400;\r\n  border-radius: 30px;\r\n  cursor: pointer;\r\n  letter-spacing: 0.4px;\r\n  font-size: 16px;\r\n  \r\n  &.light {\r\n    color: black;\r\n    background-color: white;\r\n  }\r\n  \r\n  &.dark {\r\n    color: white;\r\n    background-color: var(--c-dark-green);\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3NoYXJlZC9CdXR0b25zL1NtYWxsQnV0dG9uLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBdUM7QUFFeEIsU0FBU0MsWUFBWSxFQUFFQyxJQUFJLEVBQUVDLE1BQU0sRUFBRUMsUUFBTSxPQUFPLEVBQUU7SUFFakUscUJBQ0UsOERBQUNDO1FBQWVDLFNBQVNIO1FBQVFJLFdBQVdIO2tCQUN6Q0Y7Ozs7OztBQUdQO0FBR0EsTUFBTUcsaUJBQWlCTCw0REFBVTs7O0VBQUEsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBa0JsQyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHBzZy1uZXh0Ly4vY29tcG9uZW50cy9zaGFyZWQvQnV0dG9ucy9TbWFsbEJ1dHRvbi5qc3g/YjhhNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgc3R5bGVkIGZyb20gXCJzdHlsZWQtY29tcG9uZW50c1wiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU21hbGxCdXR0b24oeyB0ZXh0LCBhY3Rpb24sIHRoZW1lPVwibGlnaHRcIiB9KSB7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8U3RsU21hbGxCdXR0b24gb25DbGljaz17YWN0aW9ufSBjbGFzc05hbWU9e3RoZW1lfT5cclxuICAgICAge3RleHR9XHJcbiAgICA8L1N0bFNtYWxsQnV0dG9uPlxyXG4gIClcclxufVxyXG5cclxuXHJcbmNvbnN0IFN0bFNtYWxsQnV0dG9uID0gc3R5bGVkLmRpdmBcclxuICBwYWRkaW5nOiA2cHggMjBweDtcclxuICBmb250LWZhbWlseTogU3dpdHplciwgc2Fucy1zZXJpZjtcclxuICBmb250LXdlaWdodDogNDAwO1xyXG4gIGJvcmRlci1yYWRpdXM6IDMwcHg7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIGxldHRlci1zcGFjaW5nOiAwLjRweDtcclxuICBmb250LXNpemU6IDE2cHg7XHJcbiAgXHJcbiAgJi5saWdodCB7XHJcbiAgICBjb2xvcjogYmxhY2s7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcclxuICB9XHJcbiAgXHJcbiAgJi5kYXJrIHtcclxuICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWMtZGFyay1ncmVlbik7XHJcbiAgfVxyXG5gOyJdLCJuYW1lcyI6WyJzdHlsZWQiLCJTbWFsbEJ1dHRvbiIsInRleHQiLCJhY3Rpb24iLCJ0aGVtZSIsIlN0bFNtYWxsQnV0dG9uIiwib25DbGljayIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/shared/Buttons/SmallButton.jsx\n");

/***/ }),

/***/ "./components/shared/Buttons/index.js":
/*!********************************************!*\
  !*** ./components/shared/Buttons/index.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedArrowButton: () => (/* reexport safe */ _AnimatedArrowButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   AnimatedTextButton: () => (/* reexport safe */ _AnimatedTextButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   SmallButton: () => (/* reexport safe */ _SmallButton__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _SmallButton__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SmallButton */ \"./components/shared/Buttons/SmallButton.jsx\");\n/* harmony import */ var _AnimatedArrowButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AnimatedArrowButton */ \"./components/shared/Buttons/AnimatedArrowButton.jsx\");\n/* harmony import */ var _AnimatedTextButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AnimatedTextButton */ \"./components/shared/Buttons/AnimatedTextButton.jsx\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3NoYXJlZC9CdXR0b25zL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF3QztBQUNnQjtBQUNGO0FBTXJEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHBzZy1uZXh0Ly4vY29tcG9uZW50cy9zaGFyZWQvQnV0dG9ucy9pbmRleC5qcz83ZGU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTbWFsbEJ1dHRvbiBmcm9tIFwiLi9TbWFsbEJ1dHRvblwiO1xyXG5pbXBvcnQgQW5pbWF0ZWRBcnJvd0J1dHRvbiBmcm9tIFwiLi9BbmltYXRlZEFycm93QnV0dG9uXCI7XHJcbmltcG9ydCBBbmltYXRlZFRleHRCdXR0b24gZnJvbSBcIi4vQW5pbWF0ZWRUZXh0QnV0dG9uXCI7XHJcblxyXG5leHBvcnQge1xyXG4gIFNtYWxsQnV0dG9uLFxyXG4gIEFuaW1hdGVkQXJyb3dCdXR0b24sXHJcbiAgQW5pbWF0ZWRUZXh0QnV0dG9uXHJcbn0iXSwibmFtZXMiOlsiU21hbGxCdXR0b24iLCJBbmltYXRlZEFycm93QnV0dG9uIiwiQW5pbWF0ZWRUZXh0QnV0dG9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/shared/Buttons/index.js\n");

/***/ }),

/***/ "./components/shared/CondLink.js":
/*!***************************************!*\
  !*** ./components/shared/CondLink.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CondLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var utils_string_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! utils/string.utils */ \"./utils/string.utils.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction CondLink({ link, children }) {\n    return (0,utils_string_utils__WEBPACK_IMPORTED_MODULE_1__.isLinkExternal)(link) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        target: \"_blank\",\n        rel: \"noreferrer\",\n        href: link,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\CondLink.js\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        href: link,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\CondLink.js\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3NoYXJlZC9Db25kTGluay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQW9EO0FBQ3ZCO0FBRWQsU0FBU0UsU0FBUyxFQUFFQyxJQUFJLEVBQUVDLFFBQVEsRUFBRTtJQUNqRCxPQUFPSixrRUFBY0EsQ0FBQ0csc0JBQ3BCLDhEQUFDRTtRQUFFQyxRQUFPO1FBQVNDLEtBQUk7UUFBYUMsTUFBTUw7a0JBQU9DOzs7Ozs2QkFFakQsOERBQUNILGtEQUFJQTtRQUFDTyxNQUFNTDtrQkFBT0M7Ozs7OztBQUN2QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Rwc2ctbmV4dC8uL2NvbXBvbmVudHMvc2hhcmVkL0NvbmRMaW5rLmpzPzViODAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNMaW5rRXh0ZXJuYWwgfSBmcm9tIFwidXRpbHMvc3RyaW5nLnV0aWxzXCI7XHJcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENvbmRMaW5rKHsgbGluaywgY2hpbGRyZW4gfSkge1xyXG4gIHJldHVybiBpc0xpbmtFeHRlcm5hbChsaW5rKSA/XHJcbiAgICA8YSB0YXJnZXQ9XCJfYmxhbmtcIiByZWw9XCJub3JlZmVycmVyXCIgaHJlZj17bGlua30+e2NoaWxkcmVufTwvYT5cclxuICAgIDpcclxuICAgIDxMaW5rIGhyZWY9e2xpbmt9PntjaGlsZHJlbn08L0xpbms+XHJcbn1cclxuIl0sIm5hbWVzIjpbImlzTGlua0V4dGVybmFsIiwiTGluayIsIkNvbmRMaW5rIiwibGluayIsImNoaWxkcmVuIiwiYSIsInRhcmdldCIsInJlbCIsImhyZWYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/shared/CondLink.js\n");

/***/ }),

/***/ "./components/shared/atoms/Buttons/BigCta.js":
/*!***************************************************!*\
  !*** ./components/shared/atoms/Buttons/BigCta.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BigCta)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var utils_string_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! utils/string.utils */ \"./utils/string.utils.js\");\n/* harmony import */ var _styles_device__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../styles/device */ \"./styles/device.js\");\n\n\n\n\n\n/**\r\n *\r\n * @param text\r\n * @param link (optionnel)\r\n * @param onClickFunction (optionnel)\r\n * @param theme \"light\" ou \"dark\"\r\n * @return {JSX.Element}\r\n * @constructor\r\n */ function BigCta({ text, link, onClickFunction, theme, outline = false, fullWidth = true }) {\n    const backgroundColor = outline ? \"transparent\" : theme === \"dark\" ? \"var(--c-dark-green)\" : \"var(--c-soft-cream)\";\n    const textColor = outline ? theme === \"dark\" ? \"var(--c-dark-green)\" : \"var(--c-soft-cream)\" : theme === \"dark\" ? \"var(--c-soft-cream)\" : \"var(--c-dark-green)\";\n    const Cta = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ButtonWrapper, {\n        fullWidth: fullWidth,\n        text: text,\n        theme: theme,\n        textColor: textColor,\n        backgroundColor: backgroundColor,\n        className: \"cta-big\",\n        outline: outline,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            children: text\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\atoms\\\\Buttons\\\\BigCta.js\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\atoms\\\\Buttons\\\\BigCta.js\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n    if ((0,utils_string_utils__WEBPACK_IMPORTED_MODULE_3__.isLinkExternal)(link)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: link,\n            rel: \"noopener noreferrer\",\n            onClick: onClickFunction || null,\n            target: \"_blank\",\n            children: Cta\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\atoms\\\\Buttons\\\\BigCta.js\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        href: link,\n        onClick: onClickFunction || null,\n        children: Cta\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\atoms\\\\Buttons\\\\BigCta.js\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n;\nconst ButtonWrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().button.withConfig({\n    displayName: \"BigCta__ButtonWrapper\",\n    componentId: \"sc-a61d8ff1-0\"\n})`\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 10px 40px;\r\n  border-radius: 100px;\r\n  border: 1px solid ${(p)=>p.outline ? p.textColor : \"transparent\"};\r\n  width: ${(p)=>p.fullWidth ? \"100%\" : \"auto\"};\r\n\r\n  /* Corrects font smoothing for webkit */\r\n  -webkit-font-smoothing: inherit;\r\n  -moz-osx-font-smoothing: inherit;\r\n\r\n  /* Corrects inability to style clickable \\`input\\` types in iOS */\r\n  -webkit-appearance: none;\r\n  \r\n  background-color: ${(p)=>p.backgroundColor};\r\n  \r\n  p {\r\n    margin: 0;\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    font-family: Switzer, sans-serif;\r\n    color: ${(p)=>p.textColor};\r\n  }\r\n  \r\n  cursor: pointer;\r\n  \r\n  &:hover {\r\n    background-color: var(--c-brand-lighter);\r\n    P {\r\n      color: var(--c-soft-cream);\r\n    }\r\n  }\r\n  \r\n  @media ${_styles_device__WEBPACK_IMPORTED_MODULE_4__.device.tablet} {\r\n    width: inherit;\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/atoms/Buttons/BigCta.js\n");

/***/ }),

/***/ "./components/shared/inputs/index.js":
/*!*******************************************!*\
  !*** ./components/shared/inputs/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toggle: () => (/* reexport safe */ _toggle__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _toggle__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./toggle */ \"./components/shared/inputs/toggle.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3NoYXJlZC9pbnB1dHMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEI7QUFJN0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90cHNnLW5leHQvLi9jb21wb25lbnRzL3NoYXJlZC9pbnB1dHMvaW5kZXguanM/NWFiMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgVG9nZ2xlIGZyb20gXCIuL3RvZ2dsZVwiO1xyXG5cclxuZXhwb3J0IHtcclxuICBUb2dnbGVcclxufSJdLCJuYW1lcyI6WyJUb2dnbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/shared/inputs/index.js\n");

/***/ }),

/***/ "./components/shared/inputs/toggle.js":
/*!********************************************!*\
  !*** ./components/shared/inputs/toggle.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Toggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction Toggle({ isChecked, formKey, handleValueChange, label }) {\n    const [checked, setChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(isChecked);\n    function handleClick() {\n        handleValueChange(formKey, !checked);\n        setChecked(!checked);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StlToogle, {\n        checked: checked,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"toggle-button\",\n                onClick: ()=>handleClick(),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inner-dot\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\inputs\\\\toggle.js\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\inputs\\\\toggle.js\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"toggle-label\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\inputs\\\\toggle.js\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\inputs\\\\toggle.js\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nconst StlToogle = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"toggle__StlToogle\",\n    componentId: \"sc-d9158d9d-0\"\n})`\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  gap: 8px;\r\n\r\n  .toggle-button {\r\n    height: 22px;\r\n    width: 38px;\r\n    border: 1.5px solid var(--c-dark-green);\r\n    background-color: ${(p)=>p.checked ? \"var(--c-dark-green)\" : \"transparent\"};\r\n    border-radius: 21px;\r\n    cursor: pointer;\r\n  }\r\n  \r\n  .inner-dot {\r\n    height: 15px;\r\n    width: 15px;\r\n    background-color: ${(p)=>p.checked ? \"white\" : \"var(--c-dark-green)\"};\r\n    border-radius: 15px;\r\n    margin-top: 2px;\r\n    margin-left: 2px;\r\n    transform: translateX(${(p)=>p.checked ? \"100%\" : \"0%\"});\r\n    transition: all 350ms;\r\n  }\r\n  \r\n  .toggle-label {\r\n    margin: 0;\r\n    padding-top: 4px;\r\n    font-size: 16px;\r\n    line-height: 21px;\r\n    font-weight: 500;\r\n    letter-spacing: 0.4px;\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/inputs/toggle.js\n");

/***/ }),

/***/ "./components/svg/chevron-down.js":
/*!****************************************!*\
  !*** ./components/svg/chevron-down.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst chevronDown = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"20\",\n        height: \"11\",\n        viewBox: \"0 0 20 11\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M19 1L10 10L1 1\",\n            stroke: \"black\",\n            strokeWidth: \"1.4\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\svg\\\\chevron-down.js\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\svg\\\\chevron-down.js\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (chevronDown);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3N2Zy9jaGV2cm9uLWRvd24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLE1BQU1BLGNBQWM7SUFDbEIscUJBQ0UsOERBQUNDO1FBQUlDLE9BQU07UUFBS0MsUUFBTztRQUFLQyxTQUFRO1FBQVlDLE1BQUs7UUFBT0MsT0FBTTtrQkFDaEUsNEVBQUNDO1lBQUtDLEdBQUU7WUFBa0JDLFFBQU87WUFBUUMsYUFBWTtZQUFNQyxlQUFjO1lBQVFDLGdCQUFlOzs7Ozs7Ozs7OztBQUd0RztBQUVBLGlFQUFlWixXQUFXQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHBzZy1uZXh0Ly4vY29tcG9uZW50cy9zdmcvY2hldnJvbi1kb3duLmpzP2JjYmUiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgY2hldnJvbkRvd24gPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxzdmcgd2lkdGg9XCIyMFwiIGhlaWdodD1cIjExXCIgdmlld0JveD1cIjAgMCAyMCAxMVwiIGZpbGw9XCJub25lXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiPlxyXG4gICAgICA8cGF0aCBkPVwiTTE5IDFMMTAgMTBMMSAxXCIgc3Ryb2tlPVwiYmxhY2tcIiBzdHJva2VXaWR0aD1cIjEuNFwiIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIi8+XHJcbiAgICA8L3N2Zz5cclxuICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGNoZXZyb25Eb3duOyJdLCJuYW1lcyI6WyJjaGV2cm9uRG93biIsInN2ZyIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsImZpbGwiLCJ4bWxucyIsInBhdGgiLCJkIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/svg/chevron-down.js\n");

/***/ }),

/***/ "./components/svg/long-arrow.js":
/*!**************************************!*\
  !*** ./components/svg/long-arrow.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst longArrow = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"icn-arrow\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: \"24\",\n            height: \"8\",\n            viewBox: \"0 0 24 8\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M17.7277 7.80001C18.0477 7.11734 18.3571 6.52001 18.6557 6.00801C18.9757 5.49601 19.2851 5.06935 19.5837 4.72801H0.927734V3.38401H19.5837C19.2851 3.02134 18.9757 2.58401 18.6557 2.07201C18.3571 1.56001 18.0477 0.973345 17.7277 0.312012H18.8477C20.1917 1.86935 21.5997 3.02134 23.0717 3.76801V4.34401C21.5997 5.06934 20.1917 6.22134 18.8477 7.80001H17.7277Z\",\n                fill: \"#F6F4F3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\svg\\\\long-arrow.js\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\svg\\\\long-arrow.js\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\svg\\\\long-arrow.js\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (longArrow);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3N2Zy9sb25nLWFycm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSxNQUFNQSxZQUFZO0lBQ2hCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFXO2tCQUNkLDRFQUFDQztZQUFJQyxPQUFNO1lBQUtDLFFBQU87WUFBSUMsU0FBUTtZQUFXQyxNQUFLO1lBQU9DLE9BQU07c0JBQzlELDRFQUFDQztnQkFDQ0MsR0FBRTtnQkFDRkgsTUFBSzs7Ozs7Ozs7Ozs7Ozs7OztBQUlmO0FBQ0EsaUVBQWVQLFNBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90cHNnLW5leHQvLi9jb21wb25lbnRzL3N2Zy9sb25nLWFycm93LmpzPzY2YzIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbG9uZ0Fycm93ID0gKCkgPT4ge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT17XCJpY24tYXJyb3dcIn0+XHJcbiAgICAgIDxzdmcgd2lkdGg9XCIyNFwiIGhlaWdodD1cIjhcIiB2aWV3Qm94PVwiMCAwIDI0IDhcIiBmaWxsPVwibm9uZVwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj5cclxuICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgZD1cIk0xNy43Mjc3IDcuODAwMDFDMTguMDQ3NyA3LjExNzM0IDE4LjM1NzEgNi41MjAwMSAxOC42NTU3IDYuMDA4MDFDMTguOTc1NyA1LjQ5NjAxIDE5LjI4NTEgNS4wNjkzNSAxOS41ODM3IDQuNzI4MDFIMC45Mjc3MzRWMy4zODQwMUgxOS41ODM3QzE5LjI4NTEgMy4wMjEzNCAxOC45NzU3IDIuNTg0MDEgMTguNjU1NyAyLjA3MjAxQzE4LjM1NzEgMS41NjAwMSAxOC4wNDc3IDAuOTczMzQ1IDE3LjcyNzcgMC4zMTIwMTJIMTguODQ3N0MyMC4xOTE3IDEuODY5MzUgMjEuNTk5NyAzLjAyMTM0IDIzLjA3MTcgMy43NjgwMVY0LjM0NDAxQzIxLjU5OTcgNS4wNjkzNCAyMC4xOTE3IDYuMjIxMzQgMTguODQ3NyA3LjgwMDAxSDE3LjcyNzdaXCJcclxuICAgICAgICAgIGZpbGw9XCIjRjZGNEYzXCIvPlxyXG4gICAgICA8L3N2Zz5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG5leHBvcnQgZGVmYXVsdCBsb25nQXJyb3c7XHJcbiJdLCJuYW1lcyI6WyJsb25nQXJyb3ciLCJkaXYiLCJjbGFzc05hbWUiLCJzdmciLCJ3aWR0aCIsImhlaWdodCIsInZpZXdCb3giLCJmaWxsIiwieG1sbnMiLCJwYXRoIiwiZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/svg/long-arrow.js\n");

/***/ }),

/***/ "./context/CoreDataContext.js":
/*!************************************!*\
  !*** ./context/CoreDataContext.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreDataProvider: () => (/* binding */ CoreDataProvider),\n/* harmony export */   useCoreData: () => (/* binding */ useCoreData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CoreDataProvider,useCoreData auto */ \n\nconst CoreDataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction CoreDataProvider({ children }) {\n    const [coreData, setCoreData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"authors\": [],\n        \"blogs\": [],\n        \"topics\": [],\n        \"podcasts\": [],\n        \"topicGroups\": []\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // we must fetch data this way because within the Page Router, we only have client components...\n        const fetchCoreData = async ()=>{\n            const response = await fetch(\"/api/coredata\");\n            const coreData = await response.json();\n            setCoreData(coreData);\n        };\n        fetchCoreData().catch(console.error);\n    }, []);\n    return coreData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CoreDataContext.Provider, {\n        value: coreData,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\context\\\\CoreDataContext.js\",\n        lineNumber: 22,\n        columnNumber: 3\n    }, this);\n}\nfunction useCoreData() {\n    let context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CoreDataContext);\n    if (context === null) {\n        throw new Error(\"useCoreData must be used within a CoreDataProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./context/CoreDataContext.js\n");

/***/ }),

/***/ "./context/HeaderContext.js":
/*!**********************************!*\
  !*** ./context/HeaderContext.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeaderContext: () => (/* binding */ HeaderContext),\n/* harmony export */   HeaderProvider: () => (/* binding */ HeaderProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst HeaderContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nconst HeaderProvider = ({ children })=>{\n    const [headerState, setHeaderState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        showMenu: false,\n        dropDownOpen: false,\n        dropDownKey: \"blogs\"\n    });\n    const onDropDownButtonClick = (key)=>{\n        if (headerState.dropDownKey !== key) {\n            setHeaderState((prevState)=>({\n                    ...prevState,\n                    dropDownOpen: true,\n                    dropDownKey: key\n                }));\n        } else {\n            setHeaderState((prevState)=>({\n                    ...prevState,\n                    dropDownOpen: !headerState.dropDownOpen,\n                    dropDownKey: key\n                }));\n        }\n    };\n    const onDropDownClickOutside = ()=>{\n        setHeaderState((prevState)=>({\n                ...prevState,\n                dropDownOpen: false\n            }));\n    };\n    const toggleMenu = (close = false)=>{\n        let body = document.body;\n        body.classList.toggle(\"no-scroll\", !headerState.showMenu);\n        if (close) {\n            setHeaderState({\n                ...headerState,\n                showMenu: false\n            });\n            body.classList.toggle(\"no-scroll\", false);\n        } else {\n            setHeaderState({\n                ...headerState,\n                showMenu: !headerState.showMenu\n            });\n        }\n    };\n    const value = {\n        headerState,\n        toggleMenu,\n        onDropDownButtonClick,\n        onDropDownClickOutside\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\context\\\\HeaderContext.js\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./context/HeaderContext.js\n");

/***/ }),

/***/ "./hooks/useFormData.js":
/*!******************************!*\
  !*** ./hooks/useFormData.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFormData: () => (/* binding */ useFormData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useFormData = (values)=>{\n    const [formValues, setFormValues] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        ...values\n    });\n    const handleFormValueChange = (key, value)=>{\n        setFormValues({\n            ...formValues,\n            [key]: value\n        });\n    };\n    return [\n        formValues,\n        handleFormValueChange,\n        setFormValues\n    ];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VGb3JtRGF0YS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUM7QUFFMUIsTUFBTUMsY0FBYyxDQUFDQztJQUMxQixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR0osK0NBQVFBLENBQUM7UUFDM0MsR0FBR0UsTUFBTTtJQUNYO0lBRUEsTUFBTUcsd0JBQXdCLENBQUNDLEtBQUtDO1FBQ2xDSCxjQUNFO1lBQ0UsR0FBR0QsVUFBVTtZQUNiLENBQUNHLElBQUksRUFBRUM7UUFDVDtJQUVKO0lBRUEsT0FBTztRQUNMSjtRQUNBRTtRQUNBRDtLQUNEO0FBQ0gsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3Rwc2ctbmV4dC8uL2hvb2tzL3VzZUZvcm1EYXRhLmpzP2YyNGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VGb3JtRGF0YSA9ICh2YWx1ZXMpID0+IHtcclxuICBjb25zdCBbZm9ybVZhbHVlcywgc2V0Rm9ybVZhbHVlc10gPSB1c2VTdGF0ZSh7XHJcbiAgICAuLi52YWx1ZXNcclxuICB9KTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRm9ybVZhbHVlQ2hhbmdlID0gKGtleSwgdmFsdWUpID0+IHtcclxuICAgIHNldEZvcm1WYWx1ZXMoXHJcbiAgICAgIHtcclxuICAgICAgICAuLi5mb3JtVmFsdWVzLFxyXG4gICAgICAgIFtrZXldOiB2YWx1ZVxyXG4gICAgICB9XHJcbiAgICApO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiBbXHJcbiAgICBmb3JtVmFsdWVzLFxyXG4gICAgaGFuZGxlRm9ybVZhbHVlQ2hhbmdlLFxyXG4gICAgc2V0Rm9ybVZhbHVlcyxcclxuICBdXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUZvcm1EYXRhIiwidmFsdWVzIiwiZm9ybVZhbHVlcyIsInNldEZvcm1WYWx1ZXMiLCJoYW5kbGVGb3JtVmFsdWVDaGFuZ2UiLCJrZXkiLCJ2YWx1ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./hooks/useFormData.js\n");

/***/ }),

/***/ "./hooks/useLocalStorage.js":
/*!**********************************!*\
  !*** ./hooks/useLocalStorage.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useLocalStorage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useLocalStorage(key, initialValue) {\n    const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        if (typeof localStorage === \"undefined\") {\n            return initialValue;\n        }\n        try {\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : initialValue;\n        } catch (err) {\n            console.log(err);\n            return initialValue;\n        }\n    });\n    const setValue = (value)=>{\n        try {\n            // Allow value to be a function, so we have same API as useState\n            const valueToStore = value instanceof Function ? value(storedValue) : value;\n            // Save state\n            setStoredValue(valueToStore);\n            // Save to local storage\n            if (typeof localStorage !== \"undefined\") {\n                localStorage.setItem(key, JSON.stringify(valueToStore));\n            }\n        } catch (error) {\n            console.log(error);\n        }\n    };\n    return [\n        storedValue,\n        setValue\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useLocalStorage.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../components/layout */ \"./components/layout/index.js\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/script */ \"./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-cookie */ \"react-cookie\");\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_cookie__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    const getParams = (query, paramsToInclude)=>{\n        let params = paramsToInclude.reduce((params, param)=>{\n            // don\"t include empty params or page=1\n            if (query[param] && query[param] !== \"\" && param === \"page\" && Number(query.page) !== 1) {\n                params.push(`${param}=${query[param]}`);\n            }\n            return params;\n        }, []);\n        return params.length ? `?${params.join(\"&\")}` : \"\";\n    };\n    const createCanonicalUrl = ()=>{\n        const { pathname, query } = (0,next_router__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n        let url = \"\";\n        switch(pathname){\n            // article\n            case \"/article/[article]\":\n                url = `/article/${query.article}`;\n                break;\n            // blog\n            case \"/blog/[blog]\":\n                url = `/blog/${query.blog}`;\n                break;\n            case \"/blog/[blog]/filtres\":\n                url = `/blog/${query.blog}/filtres${getParams(query, [\n                    \"topic\",\n                    \"type\",\n                    \"page\"\n                ])}`;\n                break;\n            // categories\n            case \"/categories\":\n                url = \"/categories\";\n                break;\n            case \"/categories/[topic]\":\n                url = `/categories/${query.topic}`;\n                break;\n            case \"/categories/[topic]/ressources\":\n                url = `/categories/${query.topic}/ressources${getParams(query, [\n                    \"page\"\n                ])}`;\n                break;\n            case \"/categories/ministere/[ministry]\":\n                url = `/categories/ministere/${query.ministry}`;\n                break;\n            case \"/categories/ministere/[ministry]/ressources\":\n                url = `/categories/ministere/${query.ministry}/ressources${getParams(query, [\n                    \"page\"\n                ])}`;\n                break;\n            case \"/categories/vocation/[vocation]\":\n                url = `/categories/vocation/${query.vocation}`;\n                break;\n            case \"/categories/vocation/[vocation]/ressources\":\n                url = `/categories/vocation/${query.vocation}/ressources${getParams(query, [\n                    \"page\"\n                ])}`;\n                break;\n            // formation\n            case \"/formations\":\n                url = \"/formations\";\n                break;\n            case \"/formations/[formation]\":\n                url = `/formations/${query.formation}`;\n                break;\n            // parcours email\n            case \"/parcours-emails\":\n                url = \"/parcours-emails\";\n                break;\n            case \"/parcours-emails/[parcours]\":\n                url = `/parcours-emails/${query.parcours}`;\n                break;\n            // podcasts\n            case \"/podcasts\":\n                url = \"/podcasts\";\n                break;\n            case \"/podcasts/[podcast]\":\n                url = `/podcasts/${query.podcast}${getParams(query, [\n                    \"page\"\n                ])}`;\n                break;\n            case \"/podcasts/[podcast]/[episode]\":\n                url = `/podcasts/${query.podcast}/${query.episode}`;\n                break;\n            // webinaires\n            case \"/webinaires\":\n                url = `/webinaires${getParams(query, [\n                    \"page\"\n                ])}`;\n                break;\n            case \"/webinaires/[episode]\":\n                url = `/webinaires/${query.episode}${getParams(query, [\n                    \"page\"\n                ])}`;\n                break;\n            case \"/[page]\":\n                url = `/${query.page}`;\n                break;\n            // default\n            default:\n                url = \"\";\n        }\n        return new URL(url, \"https://toutpoursagloire.com\").href // or use process.env.NEXT_PUBLIC_URL?\n        ;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_cookie__WEBPACK_IMPORTED_MODULE_5__.CookiesProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"canonical\",\n                            href: createCanonicalUrl()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.png\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                CookieScripts()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\nfunction CookieScripts() {\n    let gaTagId = \"\";\n    const [cookie, setCookie] = (0,react_cookie__WEBPACK_IMPORTED_MODULE_5__.useCookies)([\n        \"preferences\"\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            cookie[\"preferences\"]?.analytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        async: true,\n                        src: `https://www.googletagmanager.com/gtag/js?id=${gaTagId}`,\n                        id: \"ga-url-script\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        id: \"ga-analytics-script\",\n                        children: `window.dataLayer = window.dataLayer || [];\r\n              function gtag(){ dataLayer.push(arguments); }\r\n              gtag(\"js\", new Date());\r\n              gtag(\"config\", \"${gaTagId}\");\r\n            `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            cookie[\"preferences\"]?.medias && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    id: \"marker-io-config\",\n                    children: `window.markerConfig = {\r\n                project: \"647f03e6a572cb7307800759\",\r\n                source: \"snippet\" };`\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n                    lineNumber: 159,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false)\n        ]\n    }, void 0, true);\n}\nfunction GoogleAnalytics() {\n    let gaTagId = \"\";\n    const [cookie, setCookie] = (0,react_cookie__WEBPACK_IMPORTED_MODULE_5__.useCookies)([\n        \"preferences\"\n    ]);\n    if (!cookie[\"preferences\"]?.analytics) {\n        return;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_4___default()), {\n                async: true,\n                src: `https://www.googletagmanager.com/gtag/js?id=${gaTagId}`,\n                id: \"ga-url-script\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_4___default()), {\n                id: \"ga-analytics-script\",\n                children: `window.dataLayer = window.dataLayer || [];\r\n        function gtag(){ dataLayer.push(arguments); }\r\n        gtag(\"js\", new Date());\r\n        gtag(\"config\", \"${gaTagId}\");\r\n         `\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_app.js\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyDocument)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_global__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/global */ \"./styles/global.js\");\n\n\n\n\nclass MyDocument extends (next_document__WEBPACK_IMPORTED_MODULE_1___default()) {\n    static async getInitialProps(ctx) {\n        const sheet = new styled_components__WEBPACK_IMPORTED_MODULE_2__.ServerStyleSheet();\n        const originalRenderPage = ctx.renderPage;\n        try {\n            ctx.renderPage = ()=>originalRenderPage({\n                    enhanceApp: (App)=>(props)=>sheet.collectStyles(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n                                lang: \"fr\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_styles_global__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                                        lineNumber: 16,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(App, {\n                                        ...props\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                                        lineNumber: 17,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                                lineNumber: 15,\n                                columnNumber: 15\n                            }, this))\n                });\n            const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_1___default().getInitialProps(ctx);\n            return {\n                ...initialProps,\n                styles: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        initialProps.styles,\n                        sheet.getStyleElement()\n                    ]\n                }, void 0, true)\n            };\n        } finally{\n            sheet.seal();\n        }\n    }\n    render() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n            lang: \"fr\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://fonts.googleapis.com\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://fonts.gstatic.com\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            href: \"https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap\",\n                            rel: \"stylesheet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.js\n");

/***/ }),

/***/ "./styles/device.js":
/*!**************************!*\
  !*** ./styles/device.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   device: () => (/* binding */ device)\n/* harmony export */ });\nconst size = {\n    mini: \"320px\",\n    tablet: \"744px\",\n    desktop: \"1024px\",\n    desktopXL: \"1441px\"\n};\nconst device = {\n    mini: `(max-width: ${size.mini})`,\n    tablet: `(min-width: ${size.tablet})`,\n    desktop: `(min-width: ${size.desktop})`,\n    desktopXL: `(min-width: ${size.desktopXL})`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zdHlsZXMvZGV2aWNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSxPQUFPO0lBQ1hDLE1BQU07SUFDTkMsUUFBUTtJQUNSQyxTQUFTO0lBQ1RDLFdBQVc7QUFDYjtBQUVPLE1BQU1DLFNBQVM7SUFDcEJKLE1BQU0sQ0FBQyxZQUFZLEVBQUVELEtBQUtDLElBQUksQ0FBQyxDQUFDLENBQUM7SUFDakNDLFFBQVEsQ0FBQyxZQUFZLEVBQUVGLEtBQUtFLE1BQU0sQ0FBQyxDQUFDLENBQUM7SUFDckNDLFNBQVMsQ0FBQyxZQUFZLEVBQUVILEtBQUtHLE9BQU8sQ0FBQyxDQUFDLENBQUM7SUFDdkNDLFdBQVcsQ0FBQyxZQUFZLEVBQUVKLEtBQUtJLFNBQVMsQ0FBQyxDQUFDLENBQUM7QUFDN0MsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3Rwc2ctbmV4dC8uL3N0eWxlcy9kZXZpY2UuanM/ZDEwYiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzaXplID0ge1xyXG4gIG1pbmk6IFwiMzIwcHhcIixcclxuICB0YWJsZXQ6IFwiNzQ0cHhcIixcclxuICBkZXNrdG9wOiBcIjEwMjRweFwiLFxyXG4gIGRlc2t0b3BYTDogXCIxNDQxcHhcIixcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBkZXZpY2UgPSB7XHJcbiAgbWluaTogYChtYXgtd2lkdGg6ICR7c2l6ZS5taW5pfSlgLCAvLyBtYXggd2lkdGggZm9yIG1pbmlcclxuICB0YWJsZXQ6IGAobWluLXdpZHRoOiAke3NpemUudGFibGV0fSlgLFxyXG4gIGRlc2t0b3A6IGAobWluLXdpZHRoOiAke3NpemUuZGVza3RvcH0pYCxcclxuICBkZXNrdG9wWEw6IGAobWluLXdpZHRoOiAke3NpemUuZGVza3RvcFhMfSlgLFxyXG59O1xyXG4iXSwibmFtZXMiOlsic2l6ZSIsIm1pbmkiLCJ0YWJsZXQiLCJkZXNrdG9wIiwiZGVza3RvcFhMIiwiZGV2aWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./styles/device.js\n");

/***/ }),

/***/ "./styles/global.js":
/*!**************************!*\
  !*** ./styles/global.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Global: () => (/* binding */ Global),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n\n\nconst Global = styled_components__WEBPACK_IMPORTED_MODULE_0__.createGlobalStyle`\r\n\r\n  // TODO: Renommer les variables correctement\r\n  :root {\r\n    \r\n    /**\r\n     * COLORS\r\n     */\r\n    --soft-white: #FAF7F3;\r\n    --c-cream: #FFEBD8;\r\n    --c-soft-cream: #F9F1E6;\r\n    --c-cream-A80: rgba(255, 235, 216, 0.8);\r\n    --c-cream-A40: rgba(255, 235, 216, 0.4);\r\n    --c-cream-A20: rgba(255, 235, 216, 0.2);\r\n    --soft-dark: #161616;\r\n    //--blue-dark: #081921;\r\n    --blue-dark: #081D21;\r\n    --c--blue-medium: #1C373C;\r\n    --c-dark-green: #081D21;\r\n    --brand-color: #EE5131;\r\n    --c-brand-light: #F3673B;\r\n    --c-brand-lighter: #FA7051;\r\n\r\n    --mobile-gap: 24px;\r\n    --tablet-gap: 60px;\r\n    --desktop-gap: 80px;\r\n    --desktopxl-gap: 96px;\r\n    --max-page-width: 1380px;\r\n\r\n    --border-space: 24px; // valeur des marges de la page;\r\n    --spacing-l: 48px;\r\n\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      --border-space: 60px;\r\n      --spacing-l: 52px;\r\n    }\r\n\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      --border-space: 64px;\r\n      --spacing-l: 96px;\r\n    }\r\n\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n      --border-space: calc((100vw + 32px - var(--max-page-width)) / 2);\r\n      --spacing-l: 96px;\r\n    }\r\n\r\n    // Spacing\r\n    --fluid-space-m: clamp(3rem, 0.22rem + 5.97vw, 5rem);\r\n  }\r\n\r\n  body {\r\n    width: 100%;\r\n    overflow-x: hidden;\r\n    //overflow-y: scroll;\r\n    margin: auto;\r\n    padding-top: 80px;\r\n    background-color: var(--soft-white);\r\n    font-family: Stelvio, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,\r\n    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;\r\n  }\r\n\r\n  html {\r\n    width: 100%;\r\n    background-color: white;\r\n    //background-color: var(--soft-white);\r\n    overscroll-behavior-y: none;\r\n  }\r\n\r\n  .site-padding {\r\n    padding-left: var(--mobile-gap);\r\n    padding-right: var(--mobile-gap);\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      padding-left: var(--tablet-gap);\r\n      padding-right: var(--tablet-gap);\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      padding-left: var(--desktop-gap);\r\n      padding-right: var(--desktop-gap);\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n      max-width: 1380px;\r\n      margin: auto;\r\n      padding-left: 32px;\r\n      padding-right: 32px;\r\n    }\r\n  }\r\n\r\n  .site-padding-left {\r\n    padding-left: var(--mobile-gap) !important;\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      padding-left: var(--tablet-gap) !important;\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      padding-left: var(--desktop-gap) !important;\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n      padding-left: calc(32px + (100vw - 1380px) / 2) !important;\r\n    }\r\n  }\r\n\r\n  .site-padding-right {\r\n    padding-right: var(--mobile-gap) !important;\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      padding-right: var(--tablet-gap) !important;\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      padding-right: var(--desktop-gap) !important;\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n      padding-right: calc(32px + (100vw - 1380px) / 2) !important;\r\n    }\r\n  }\r\n\r\n  .site-margin {\r\n    margin-left: var(--mobile-gap);\r\n    margin-right: var(--mobile-gap);\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      margin-left: var(--mobile-gap);\r\n      margin-right: var(--mobile-gap);\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      margin-left: var(--desktop-gap);\r\n      margin-right: var(--desktop-gap);\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n      max-width: var(--max-page-width);\r\n      margin: auto;\r\n    }\r\n  }\r\n\r\n  .fw-background {\r\n    &:before {\r\n      content: '';\r\n      position: absolute;\r\n      width: 105vw;\r\n      height: 100%;\r\n      top: 0;\r\n      left: -24px;\r\n      @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n        left: -60px;\r\n      }\r\n      @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n        left: -80px;\r\n      }\r\n      @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n        left: -96px;\r\n      }\r\n      background-color: #080808;\r\n      z-index: -1;\r\n    }\r\n  }\r\n\r\n\r\n  .mobile-hide {\r\n    display: none;\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      display: inherit;\r\n    }\r\n  }\r\n\r\n  .mobile-hide_flex {\r\n    display: none !important;\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      display: flex !important;\r\n    }\r\n  }\r\n\r\n  .tablet-hide_flex {\r\n    display: none !important;\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      display: flex !important;\r\n    }\r\n  }\r\n\r\n  .svg-filter {\r\n    height: 0;\r\n    left: -9999em;\r\n    margin: 0;\r\n    padding: 0;\r\n    position: absolute;\r\n    width: 0;\r\n  }\r\n/* \r\n  .with-duotone {\r\n    filter: url('#duotone-filter');\r\n  } */\r\n\r\n  .no-select {\r\n    * {\r\n      -webkit-touch-callout: none;\r\n      -webkit-user-select: none;\r\n      -moz-user-select: none;\r\n      -ms-user-select: none;\r\n      user-select: none;\r\n    }\r\n  }\r\n\r\n  .no-scroll {\r\n    overflow: hidden;\r\n  }\r\n  \r\n  .primary-hover:hover {\r\n\t\t  color: var(--brand-color) !important;\r\n  }\r\n`;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Global);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./styles/global.js\n");

/***/ }),

/***/ "./utils/date.utils.js":
/*!*****************************!*\
  !*** ./utils/date.utils.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dateDiffInDays: () => (/* binding */ dateDiffInDays),\n/* harmony export */   dateForHumans: () => (/* binding */ dateForHumans),\n/* harmony export */   hour: () => (/* binding */ hour)\n/* harmony export */ });\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dayjs */ \"dayjs\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var dayjs_locale_fr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs/locale/fr */ \"dayjs/locale/fr\");\n/* harmony import */ var dayjs_locale_fr__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_fr__WEBPACK_IMPORTED_MODULE_1__);\n\n\ndayjs__WEBPACK_IMPORTED_MODULE_0___default().locale(\"fr\");\nfunction dateForHumans(date) {\n    // const template = dayjs(date).year() !== dayjs().year() ? \"DD MMM YYYY\" : \"DD MMM\";\n    const template = \"DD MMM YYYY\";\n    return dayjs__WEBPACK_IMPORTED_MODULE_0___default()(date).locale(\"fr\").format(template);\n}\nfunction hour(date) {\n    return dayjs__WEBPACK_IMPORTED_MODULE_0___default()(date).locale(\"fr\").format(\"HH:mm\");\n}\nfunction dateDiffInDays(a, b) {\n    const _MS_PER_DAY = 1000 * 60 * 60 * 24;\n    // Discard the time and time-zone information.\n    const utc1 = toUTC(a);\n    const utc2 = toUTC(b);\n    return Math.floor(Math.abs(utc2 - utc1) / _MS_PER_DAY);\n}\nfunction toUTC(date) {\n    return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9kYXRlLnV0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBMEI7QUFDRDtBQUV6QkEsbURBQVksQ0FBQztBQUViLFNBQVNFLGNBQWNDLElBQUk7SUFDekIscUZBQXFGO0lBQ3JGLE1BQU1DLFdBQVc7SUFDakIsT0FBT0osNENBQUtBLENBQUNHLE1BQU1GLE1BQU0sQ0FBQyxNQUFNSSxNQUFNLENBQUNEO0FBQ3pDO0FBRUEsU0FBU0UsS0FBS0gsSUFBSTtJQUNoQixPQUFPSCw0Q0FBS0EsQ0FBQ0csTUFBTUYsTUFBTSxDQUFDLE1BQU1JLE1BQU0sQ0FBQztBQUN6QztBQUVBLFNBQVNFLGVBQWVDLENBQUMsRUFBRUMsQ0FBQztJQUMxQixNQUFNQyxjQUFjLE9BQU8sS0FBSyxLQUFLO0lBQ3JDLDhDQUE4QztJQUM5QyxNQUFNQyxPQUFPQyxNQUFNSjtJQUNuQixNQUFNSyxPQUFPRCxNQUFNSDtJQUVuQixPQUFPSyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLEdBQUcsQ0FBRUgsT0FBT0YsUUFBU0Q7QUFDOUM7QUFFQSxTQUFTRSxNQUFNVCxJQUFJO0lBQ2pCLE9BQU9jLEtBQUtDLEdBQUcsQ0FBQ2YsS0FBS2dCLGNBQWMsSUFBSWhCLEtBQUtpQixXQUFXLElBQ3JEakIsS0FBS2tCLFVBQVUsSUFBSWxCLEtBQUttQixXQUFXLElBQ25DbkIsS0FBS29CLGFBQWEsSUFBSXBCLEtBQUtxQixhQUFhO0FBQzVDO0FBTUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90cHNnLW5leHQvLi91dGlscy9kYXRlLnV0aWxzLmpzPzZlMzAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGRheWpzIGZyb20gXCJkYXlqc1wiO1xyXG5pbXBvcnQgXCJkYXlqcy9sb2NhbGUvZnJcIjtcclxuXHJcbmRheWpzLmxvY2FsZShcImZyXCIpO1xyXG5cclxuZnVuY3Rpb24gZGF0ZUZvckh1bWFucyhkYXRlKSB7XHJcbiAgLy8gY29uc3QgdGVtcGxhdGUgPSBkYXlqcyhkYXRlKS55ZWFyKCkgIT09IGRheWpzKCkueWVhcigpID8gXCJERCBNTU0gWVlZWVwiIDogXCJERCBNTU1cIjtcclxuICBjb25zdCB0ZW1wbGF0ZSA9IFwiREQgTU1NIFlZWVlcIjtcclxuICByZXR1cm4gZGF5anMoZGF0ZSkubG9jYWxlKFwiZnJcIikuZm9ybWF0KHRlbXBsYXRlKVxyXG59XHJcblxyXG5mdW5jdGlvbiBob3VyKGRhdGUpIHtcclxuICByZXR1cm4gZGF5anMoZGF0ZSkubG9jYWxlKFwiZnJcIikuZm9ybWF0KFwiSEg6bW1cIik7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIGRhdGVEaWZmSW5EYXlzKGEsIGIpIHtcclxuICBjb25zdCBfTVNfUEVSX0RBWSA9IDEwMDAgKiA2MCAqIDYwICogMjQ7XHJcbiAgLy8gRGlzY2FyZCB0aGUgdGltZSBhbmQgdGltZS16b25lIGluZm9ybWF0aW9uLlxyXG4gIGNvbnN0IHV0YzEgPSB0b1VUQyhhKTtcclxuICBjb25zdCB1dGMyID0gdG9VVEMoYik7XHJcblxyXG4gIHJldHVybiBNYXRoLmZsb29yKE1hdGguYWJzKCh1dGMyIC0gdXRjMSkpIC8gX01TX1BFUl9EQVkpO1xyXG59XHJcblxyXG5mdW5jdGlvbiB0b1VUQyhkYXRlKSB7XHJcbiAgcmV0dXJuIERhdGUuVVRDKGRhdGUuZ2V0VVRDRnVsbFllYXIoKSwgZGF0ZS5nZXRVVENNb250aCgpLFxyXG4gICAgZGF0ZS5nZXRVVENEYXRlKCksIGRhdGUuZ2V0VVRDSG91cnMoKSxcclxuICAgIGRhdGUuZ2V0VVRDTWludXRlcygpLCBkYXRlLmdldFVUQ1NlY29uZHMoKSk7XHJcbn1cclxuXHJcbmV4cG9ydCB7XHJcbiAgZGF0ZUZvckh1bWFucyxcclxuICBkYXRlRGlmZkluRGF5cyxcclxuICBob3VyXHJcbn1cclxuIl0sIm5hbWVzIjpbImRheWpzIiwibG9jYWxlIiwiZGF0ZUZvckh1bWFucyIsImRhdGUiLCJ0ZW1wbGF0ZSIsImZvcm1hdCIsImhvdXIiLCJkYXRlRGlmZkluRGF5cyIsImEiLCJiIiwiX01TX1BFUl9EQVkiLCJ1dGMxIiwidG9VVEMiLCJ1dGMyIiwiTWF0aCIsImZsb29yIiwiYWJzIiwiRGF0ZSIsIlVUQyIsImdldFVUQ0Z1bGxZZWFyIiwiZ2V0VVRDTW9udGgiLCJnZXRVVENEYXRlIiwiZ2V0VVRDSG91cnMiLCJnZXRVVENNaW51dGVzIiwiZ2V0VVRDU2Vjb25kcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./utils/date.utils.js\n");

/***/ }),

/***/ "./utils/image-utils.js":
/*!******************************!*\
  !*** ./utils/image-utils.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withRealSrc: () => (/* binding */ withRealSrc)\n/* harmony export */ });\nfunction withRealSrc(image) {\n    if (image?.provider === \"local\") {\n        return \"http://localhost:1337\" + image.url;\n    }\n    if (image?.url) return image.url;\n    return undefined;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9pbWFnZS11dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsWUFBWUMsS0FBSztJQUN4QixJQUFJQSxPQUFPQyxhQUFhLFNBQVM7UUFDL0IsT0FBTywwQkFBMEJELE1BQU1FLEdBQUc7SUFDNUM7SUFDQSxJQUFJRixPQUFPRSxLQUFLLE9BQU9GLE1BQU1FLEdBQUc7SUFDaEMsT0FBT0M7QUFDVDtBQUlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHBzZy1uZXh0Ly4vdXRpbHMvaW1hZ2UtdXRpbHMuanM/NDRlNiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiB3aXRoUmVhbFNyYyhpbWFnZSkge1xyXG4gIGlmIChpbWFnZT8ucHJvdmlkZXIgPT09IFwibG9jYWxcIikge1xyXG4gICAgcmV0dXJuIFwiaHR0cDovL2xvY2FsaG9zdDoxMzM3XCIgKyBpbWFnZS51cmxcclxuICB9XHJcbiAgaWYgKGltYWdlPy51cmwpIHJldHVybiBpbWFnZS51cmw7XHJcbiAgcmV0dXJuIHVuZGVmaW5lZDtcclxufVxyXG5cclxuZXhwb3J0IHtcclxuICB3aXRoUmVhbFNyY1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ3aXRoUmVhbFNyYyIsImltYWdlIiwicHJvdmlkZXIiLCJ1cmwiLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/image-utils.js\n");

/***/ }),

/***/ "./utils/list.utils.js":
/*!*****************************!*\
  !*** ./utils/list.utils.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dynamicSort: () => (/* binding */ dynamicSort),\n/* harmony export */   sortByDate: () => (/* binding */ sortByDate),\n/* harmony export */   topicSort: () => (/* binding */ topicSort)\n/* harmony export */ });\n/**\r\n * Tri une liste de topics en ajoutant les enfants de chaque topic\r\n * dans un nouvel attribut \"children\".\r\n * @param topics\r\n * @returns [{}]\r\n */ function topicSort(topics) {\n    const sortedList = [];\n    const cpyTopics = topics.map((topic)=>({\n            ...topic,\n            children: null\n        }));\n    cpyTopics.filter((t)=>t.parent == null || t.id === cpyTopics[0].id).map((N1Topic)=>{\n        cpyTopics.filter((t)=>t.parent?.id === N1Topic.id).map((N2Topic)=>{\n            cpyTopics.filter((t)=>t.parent?.id === N2Topic.id).map((N3Topic)=>{\n                N2Topic.children = N2Topic.children ? N2Topic.children : [];\n                N2Topic.children.push(N3Topic);\n            });\n            N2Topic.children && N2Topic.children.sort(dynamicSort(\"name\"));\n            N1Topic.children = N1Topic.children ? N1Topic.children : [];\n            N1Topic.children.push(N2Topic);\n        });\n        N1Topic.children && N1Topic.children.sort(dynamicSort(\"name\"));\n        sortedList.push(N1Topic);\n    });\n    return sortedList;\n}\nconst dynamicSort = (property)=>{\n    let sortOrder = 1;\n    if (property[0] === \"-\") {\n        sortOrder = -1;\n        property = property.substr(1);\n    }\n    return function(a, b) {\n        let result = a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;\n        return result * sortOrder;\n    };\n};\nfunction sortByDate(a, b) {\n    return new Date(b.published_at) - new Date(a.published_at);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/list.utils.js\n");

/***/ }),

/***/ "./utils/string.utils.js":
/*!*******************************!*\
  !*** ./utils/string.utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getYouTubeVideoIdFromUrl: () => (/* binding */ getYouTubeVideoIdFromUrl),\n/* harmony export */   isLinkExternal: () => (/* binding */ isLinkExternal),\n/* harmony export */   noHyphen: () => (/* binding */ noHyphen),\n/* harmony export */   removeHtml: () => (/* binding */ removeHtml),\n/* harmony export */   removeLastBackSlash: () => (/* binding */ removeLastBackSlash),\n/* harmony export */   removeMarkdown: () => (/* binding */ removeMarkdown),\n/* harmony export */   reverseWords: () => (/* binding */ reverseWords),\n/* harmony export */   slugify: () => (/* binding */ slugify)\n/* harmony export */ });\nfunction slugify(str) {\n    str = str.replace(/^\\s+|\\s+$/g, \"\");\n    str = str.toLowerCase();\n    let from = \"\\xc1\\xc4\\xc2\\xc0\\xc3\\xc5Č\\xc7ĆĎ\\xc9Ě\\xcb\\xc8\\xcaẼĔȆ\\xcd\\xcc\\xce\\xcfŇ\\xd1\\xd3\\xd6\\xd2\\xd4\\xd5\\xd8ŘŔŠŤ\\xdaŮ\\xdc\\xd9\\xdb\\xddŸŽ\\xe1\\xe4\\xe2\\xe0\\xe3\\xe5č\\xe7ćď\\xe9ě\\xeb\\xe8\\xeaẽĕȇ\\xed\\xec\\xee\\xefň\\xf1\\xf3\\xf6\\xf2\\xf4\\xf5\\xf8\\xf0řŕšť\\xfaů\\xfc\\xf9\\xfb\\xfd\\xffž\\xfe\\xdeĐđ\\xdf\\xc6a\\xb7/_,:;\";\n    let to = \"AAAAAACCCDEEEEEEEEIIIINNOOOOOORRSTUUUUUYYZaaaaaacccdeeeeeeeeiiiinnooooooorrstuuuuuyyzbBDdBAa------\";\n    for(let i = 0, l = from.length; i < l; i++){\n        str = str.replace(new RegExp(from.charAt(i), \"g\"), to.charAt(i));\n    }\n    // Remove invalid chars\n    str = str.replace(/[^a-z0-9 -]/g, \"\")// Collapse whitespace and replace by -\n    .replace(/\\s+/g, \"-\")// Collapse dashes\n    .replace(/-+/g, \"-\");\n    return str;\n}\n/**\r\n * Retourne la chaine passée en paramètre sans markdown.\r\n * Seuls les noms des liens sont conservés, pas les url.\r\n */ function removeMarkdown(text) {\n    return text.replace(/(?:_|[*#])|\\[(.*?)\\]\\(.*?\\)/gm, \"$1\");\n}\n/**\r\n * Retourne la chaine passée en paramètre sans balise html\r\n * en gardant le contenu.\r\n */ function removeHtml(str) {\n    return str.replace(/`|<[^>]*(>|…)/gm, \"\");\n}\n/**\r\n * Retourne true si le lien passé redirige vers un site externe.\r\n * @param link {string}\r\n * @returns {boolean}\r\n */ function isLinkExternal(link) {\n    return link.includes(\".\");\n}\nconst getYouTubeVideoIdFromUrl = (url)=>{\n    url = url.split(/(vi\\/|v=|\\/v\\/|youtu\\.be\\/|\\/embed\\/)/);\n    return url[2] !== undefined ? url[2].split(/[^0-9a-z_\\-]/i)[0] : url[0];\n};\n/**\r\n  * Retourne la chaîne passée en paramètre sans le \"\\\" de fin\r\n  * Utiliser pour le body des articles\r\n  */ function removeLastBackSlash(str) {\n    return str.replace(/^\\\\/gm, \"\");\n}\nfunction noHyphen(text) {\n    return text?.replace(/-/g, \" \") || \"\";\n}\nfunction reverseWords(str) {\n    const arr = str.split(\" \");\n    return [\n        arr.pop(),\n        [\n            ...arr\n        ].join(\" \")\n    ].join(\" \");\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/string.utils.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@apollo/client":
/*!*********************************!*\
  !*** external "@apollo/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@apollo/client");

/***/ }),

/***/ "dayjs":
/*!************************!*\
  !*** external "dayjs" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("dayjs");

/***/ }),

/***/ "dayjs/locale/fr":
/*!**********************************!*\
  !*** external "dayjs/locale/fr" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("dayjs/locale/fr");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-cookie":
/*!*******************************!*\
  !*** external "react-cookie" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-cookie");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-components":
/*!************************************!*\
  !*** external "styled-components" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-components");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();