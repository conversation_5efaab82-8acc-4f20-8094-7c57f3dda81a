import { device } from "/styles/device";
import styled from "styled-components";
import SectionHeader from "../shared/section/SectionHeader";
import { useInView } from "react-intersection-observer";
import { useCallback, useEffect, useRef, useState } from "react";
import AnimatedTextButton from "../shared/Buttons/AnimatedTextButton";
import { removeHtml, removeMarkdown } from "../../utils/string.utils";


export default function SectionMission({ data }) {

  const { partOne, partTwo } = data;

  const missionRef = useRef();
  const [cpt, cptInView] = useInView();

  return (
    <Wrapper ref={cpt} inView={cptInView}>
      <GradientCircle inView={cptInView}/>
      <Content ref={missionRef} >
        <SectionHeader supTitle={"Notre"} title={"Mission"} light/>
        <p className={"mission-desc"}>{removeHtml(removeMarkdown(partOne || ""))}</p>
        <div>
          <AnimatedTextButton theme={"light"} text={"En savoir plus"} link={"/a-propos"} />
        </div>
      </Content>
    </Wrapper>
  );
}

const Wrapper = styled.div`
  position: relative;
  top: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 24px;
  padding: 0 var(--border-space);
  overflow: hidden;
  height: 100vh;
  max-height: 900px;

  @media ${device.tablet} {
    align-items: center;
  }
`;

const Content = styled.div`
  grid-column: 1/3;
  margin-top: 48px;
  margin-bottom: 56px;
  width: 100%;
  z-index: 1;

  .mission-desc {
    color: var(--soft-white);
    margin-top: 20px;
    font-size: 20px;
    font-family: "Switzer", serif;
    margin-bottom: 48px;
    z-index: 2;
  }

  @media ${device.tablet} {
    font-size: 22px;
    .mission-desc {
      margin-top: 22px;
      font-size: 22px;
    }
  }
  @media ${device.desktop} {
    grid-column: 1/2;
    margin-top: 126px;
    margin-bottom: 164px;
    .mission-desc {
      margin-top: 20px;
      font-size: 20px;
    }
  }
`;

const GradientCircle = styled.div`

  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #F3673B;
  z-index: 0;

  @media ${device.desktop} {
    left: -50%;
    width: 200%;
    height: auto;
    top: -200%;
    transform: ${p => p.inView ? "scale(1) translateY(25%)" : "scale(0.2) translateY(0)" };
    aspect-ratio: 1/1;
    border-radius: 99999px;
    transform-origin: center;
    transition: all 1.5s ease-out;
  }

`;
